/*
Title: Smallest Integer Divisible by K
Content:
Given a positive integer `k`, you need to find the **length** of the **smallest** positive integer `n` such that `n` is divisible by `k`, and `n` only contains the digit `1`.

Return _the **length** of_ `n`. If there is no such `n`, return -1.

**Note:** `n` may not fit in a 64-bit signed integer.

**Example 1:**

**Input:** k = 1
**Output:** 1
**Explanation:** The smallest answer is n = 1, which has length 1.

**Example 2:**

**Input:** k = 2
**Output:** -1
**Explanation:** There is no such positive integer n divisible by 2.

**Example 3:**

**Input:** k = 3
**Output:** 3
**Explanation:** The smallest answer is n = 111, which has length 3.

**Constraints:**

*   `1 <= k <= 105`
*/

package com.example;
public class Solution90 {
    public int smallestRepunitDivByK(int k) {
        
        if (k % 2 == 0 || k % 5 == 0) {
            return -1;
        }
        
        int resid = 1 % k, len = 1;
        
        while (resid != 0) {
            
            resid = (resid * 10 + 1) % k;
            len++;
        }
        
        return len;
    }
}