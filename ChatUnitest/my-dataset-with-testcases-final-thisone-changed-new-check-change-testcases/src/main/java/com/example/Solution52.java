/*
Title: Solve the Equation
Content:
Solve a given equation and return the value of `'x'` in the form of a string `"x=#value "`. The equation contains only `'+'`, `'-'` operation, the variable `'x'` and its coefficient. You should return `"No solution "` if there is no solution for the equation, or `"Infinite solutions "` if there are infinite solutions for the equation.

If there is exactly one solution for the equation, we ensure that the value of `'x'` is an integer.

**Example 1:**

**Input:** equation =  "x+5-3+x=6+x-2 "
**Output:**  "x=2 "

**Example 2:**

**Input:** equation =  "x=x "
**Output:**  "Infinite solutions "

**Example 3:**

**Input:** equation =  "2x=x "
**Output:**  "x=0 "

**Constraints:**

*   `3 <= equation.length <= 1000`
*   `equation` has exactly one `'='`.
*   `equation` consists of integers with an absolute value in the range `[0, 100]` without any leading zeros, and the variable `'x'`.
*/

package com.example;
public class Solution52 {
    public String solveEquation(String equation) {
        int factor = 0, val = 0;
        int index = 0, n = equation.length(), sign1 = 1; 
        while (index < n) {
            if (equation.charAt(index) == '=') {
                sign1 = -1; 
                index++;
                continue;
            }

            int sign2 = sign1, number = 0;
            boolean valid = false; 
            if (equation.charAt(index) == '-' || equation.charAt(index) == '+') { 
                sign2 = (equation.charAt(index) == '-') ? -sign1 : sign1;
                index++;
            }
            while (index < n && Character.isDigit(equation.charAt(index))) {
                number = number * 10 + (equation.charAt(index) - '0');
                index++;
                valid = true;
            }

            if (index < n && equation.charAt(index) == 'x') { 
                factor += valid ? sign2 * number : sign2;
                index++;
            } else { 
                val += sign2 * number;
            }
        }

        if (factor == 0) {
            return val == 0 ? "Infinite solutions" : "No solution";
        }
        return "x=" + (-val / factor);
    }
}