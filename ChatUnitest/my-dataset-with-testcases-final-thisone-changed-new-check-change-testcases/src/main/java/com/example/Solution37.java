/*
Title: Largest Palindrome Product
Content:
Given an integer n, return _the **largest palindromic integer** that can be represented as the product of two `n`\-digits integers_. Since the answer can be very large, return it **modulo** `1337`.

**Example 1:**

**Input:** n = 2
**Output:** 987
Explanation: 99 x 91 = 9009, 9009 % 1337 = 987

**Example 2:**

**Input:** n = 1
**Output:** 9

**Constraints:**

*   `1 <= n <= 8`
*/

package com.example;
public class Solution37 {
    public int largestPalindrome(int n) {
        if (n == 1) {
            return 9;
        }
        int upper = (int) Math.pow(10, n) - 1;
        int ans = 0;
        for (int left = upper; ans == 0; --left) { 
            long p = left;
            for (int x = left; x > 0; x /= 10) {
                p = p * 10 + x % 10; 
            }
            for (long x = upper; x * x >= p; --x) {
                if (p % x == 0) { 
                    ans = (int) (p % 1337);
                    break;
                }
            }
        }
        return ans;
    }
}