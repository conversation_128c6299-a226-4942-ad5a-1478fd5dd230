/*
Title: X of a Kind in a Deck of Cards
Content:
You are given an integer array `deck` where `deck[i]` represents the number written on the `ith` card.

Partition the cards into **one or more groups** such that:

*   Each group has **exactly** `x` cards where `x > 1`, and
*   All the cards in one group have the same integer written on them.

Return `true` _if such partition is possible, or_ `false` _otherwise_.

**Example 1:**

**Input:** deck = \[1,2,3,4,4,3,2,1\]
**Output:** true
**Explanation**: Possible partition \[1,1\],\[2,2\],\[3,3\],\[4,4\].

**Example 2:**

**Input:** deck = \[1,1,1,2,2,2,3,3\]
**Output:** false
**Explanation**: No possible partition.

**Constraints:**

*   `1 <= deck.length <= 104`
*   `0 <= deck[i] < 104`
*/

package com.example;
public class Solution83 {
    public boolean hasGroupsSizeX(int[] deck) {
        int[] count = new int[10000];
        for (int c: deck) {
            count[c]++;
        }

        int g = -1;
        for (int i = 0; i < 10000; ++i) {
            if (count[i] > 0) {
                if (g == -1) {
                    g = count[i];
                } else {
                    g = gcd(g, count[i]);
                }
            }
        }
        return g >= 2;
    }

    public int gcd(int x, int y) {
        return x == 0 ? y : gcd(y % x, x);
    }
}