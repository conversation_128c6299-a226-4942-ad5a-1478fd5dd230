/*
Title: Minimum Factorization
Content:
Given a positive integer num, return _the smallest positive integer_ `x` _whose multiplication of each digit equals_ `num`. If there is no answer or the answer is not fit in **32-bit** signed integer, return `0`.

**Example 1:**

**Input:** num = 48
**Output:** 68

**Example 2:**

**Input:** num = 15
**Output:** 35

**Constraints:**

*   `1 <= num <= 231 - 1`
*/

package com.example;
public class Solution48 {
    public int smallestFactorization(int a) {
        if (a < 2)
            return a;
        long res = 0, mul = 1;
        for (int i = 9; i >= 2; i--) {
            while (a % i == 0) {
                a /= i;
                res = mul * i + res;
                mul *= 10;
            }
        }
        return a < 2 && res <= Integer.MAX_VALUE ? (int)res : 0;
    }
}