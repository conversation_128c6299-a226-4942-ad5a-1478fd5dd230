/*
Title: Super Pow
Content:
Your task is to calculate `ab` mod `1337` where `a` is a positive integer and `b` is an extremely large positive integer given in the form of an array.

**Example 1:**

**Input:** a = 2, b = \[3\]
**Output:** 8

**Example 2:**

**Input:** a = 2, b = \[1,0\]
**Output:** 1024

**Example 3:**

**Input:** a = 1, b = \[4,3,3,8,5,2\]
**Output:** 1

**Constraints:**

*   `1 <= a <= 231 - 1`
*   `1 <= b.length <= 2000`
*   `0 <= b[i] <= 9`
*   `b` does not contain leading zeros.
*/

package com.example;
public class Solution30 {
    static final int MOD = 1337;

    public int superPow(int a, int[] b) {
        int ans = 1;
        for (int i = b.length - 1; i >= 0; --i) {
            ans = (int) ((long) ans * pow(a, b[i]) % MOD);
            a = pow(a, 10);
        }
        return ans;
    }

    public int pow(int x, int n) {
        int res = 1;
        while (n != 0) {
            if (n % 2 != 0) {
                res = (int) ((long) res * x % MOD);
            }
            x = (int) ((long) x * x % MOD);
            n /= 2;
        }
        return res;
    }
}