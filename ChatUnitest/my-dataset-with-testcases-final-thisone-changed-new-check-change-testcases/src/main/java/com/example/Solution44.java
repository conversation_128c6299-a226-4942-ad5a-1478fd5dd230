/*
Title: Squirrel Simulation
Content:
You are given two integers `height` and `width` representing a garden of size `height x width`. You are also given:

*   an array `tree` where `tree = [treer, treec]` is the position of the tree in the garden,
*   an array `squirrel` where `squirrel = [squirrelr, squirrelc]` is the position of the squirrel in the garden,
*   and an array `nuts` where `nuts[i] = [nutir, nutic]` is the position of the `ith` nut in the garden.

The squirrel can only take at most one nut at one time and can move in four directions: up, down, left, and right, to the adjacent cell.

Return _the **minimal distance** for the squirrel to collect all the nuts and put them under the tree one by one_.

The **distance** is the number of moves.

**Example 1:**

**Input:** height = 5, width = 7, tree = \[2,2\], squirrel = \[4,4\], nuts = \[\[3,0\], \[2,5\]\]
**Output:** 12
**Explanation:** The squirrel should go to the nut at \[2, 5\] first to achieve a minimal distance.

**Example 2:**

**Input:** height = 1, width = 3, tree = \[0,1\], squirrel = \[0,0\], nuts = \[\[0,2\]\]
**Output:** 3

**Constraints:**

*   `1 <= height, width <= 100`
*   `tree.length == 2`
*   `squirrel.length == 2`
*   `1 <= nuts.length <= 5000`
*   `nuts[i].length == 2`
*   `0 <= treer, squirrelr, nutir <= height`
*   `0 <= treec, squirrelc, nutic <= width`
*/

package com.example;
public class Solution44 {
    public int getDistance(int[] a, int[] b) {
        return Math.abs(a[0] - b[0]) + Math.abs(a[1] - b[1]);
    }

    public int minDistance(int height, int width, int[] tree, int[] squirrel, int[][] nuts) {
        int ans = (int) 1e9;
        int sum_dis = 0;
        for (int[] nut : nuts)
            sum_dis += getDistance(nut, tree) * 2;
        for (int[] first_nut : nuts) {
            int cur = sum_dis - getDistance(first_nut, tree) + getDistance(first_nut, squirrel);
            ans = Math.min(cur, ans);
        }
        return ans;
    }
}