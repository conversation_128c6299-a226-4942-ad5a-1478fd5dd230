/*
Title: Divisor Game
Content:
<PERSON> and <PERSON> take turns playing a game, with <PERSON> starting first.

Initially, there is a number `n` on the chalkboard. On each player's turn, that player makes a move consisting of:

*   Choosing any `x` with `0 < x < n` and `n % x == 0`.
*   Replacing the number `n` on the chalkboard with `n - x`.

Also, if a player cannot make a move, they lose the game.

Return `true` _if and only if <PERSON> wins the game, assuming both players play optimally_.

**Example 1:**

**Input:** n = 2
**Output:** true
**Explanation:** <PERSON> chooses 1, and <PERSON> has no more moves.

**Example 2:**

**Input:** n = 3
**Output:** false
**Explanation:** <PERSON> chooses 1, <PERSON> chooses 1, and <PERSON> has no more moves.

**Constraints:**

*   `1 <= n <= 1000`
*/

package com.example;
public class Solution91 {
    public boolean divisorGame(int n) {
        return n % 2 == 0;
    }
}