/*
Title: Stone Game
Content:
<PERSON> and <PERSON> play a game with piles of stones. There are an **even** number of piles arranged in a row, and each pile has a **positive** integer number of stones `piles[i]`.

The objective of the game is to end with the most stones. The **total** number of stones across all the piles is **odd**, so there are no ties.

<PERSON> and <PERSON> take turns, with **<PERSON> starting first**. Each turn, a player takes the entire pile of stones either from the **beginning** or from the **end** of the row. This continues until there are no more piles left, at which point the person with the **most stones wins**.

Assuming <PERSON> and <PERSON> play optimally, return `true` _if <PERSON> wins the game, or_ `false` _if <PERSON> wins_.

**Example 1:**

**Input:** piles = \[5,3,4,5\]
**Output:** true
**Explanation:** 
<PERSON> starts first, and can only take the first 5 or the last 5.
Say she takes the first 5, so that the row becomes \[3, 4, 5\].
If <PERSON> takes 3, then the board is \[4, 5\], and <PERSON> takes 5 to win with 10 points.
If <PERSON> takes the last 5, then the board is \[3, 4\], and <PERSON> takes 4 to win with 9 points.
This demonstrated that taking the first 5 was a winning move for <PERSON>, so we return true.

**Example 2:**

**Input:** piles = \[3,7,2,3\]
**Output:** true

**Constraints:**

*   `2 <= piles.length <= 500`
*   `piles.length` is **even**.
*   `1 <= piles[i] <= 500`
*   `sum(piles[i])` is **odd**.
*/

package com.example;
public class Solution77 {
    public boolean stoneGame(int[] piles) {
        int length = piles.length;
        int[][] dp = new int[length][length];
        for (int i = 0; i < length; i++) {
            dp[i][i] = piles[i];
        }
        for (int i = length - 2; i >= 0; i--) {
            for (int j = i + 1; j < length; j++) {
                dp[i][j] = Math.max(piles[i] - dp[i + 1][j], piles[j] - dp[i][j - 1]);
            }
        }
        return dp[0][length - 1] > 0;
    }
}