/*
Title: Find the Derangement of An Array
Content:
In combinatorial mathematics, a **derangement** is a permutation of the elements of a set, such that no element appears in its original position.

You are given an integer `n`. There is originally an array consisting of `n` integers from `1` to `n` in ascending order, return _the number of **derangements** it can generate_. Since the answer may be huge, return it **modulo** `109 + 7`.

**Example 1:**

**Input:** n = 3
**Output:** 2
**Explanation:** The original array is \[1,2,3\]. The two derangements are \[2,3,1\] and \[3,1,2\].

**Example 2:**

**Input:** n = 2
**Output:** 1

**Constraints:**

*   `1 <= n <= 106`
*/

package com.example;
public class Solution51 {
    public int findDerangement(int n) {
        long mul = 1, sum = 0, M = 1000000007;
        for (int i = n; i >= 0; i--) {
            sum = (sum + M + mul * (i % 2 == 0 ? 1 : -1)) % M;
            mul = (mul * i) % M;
        }
        return (int) sum;
    }
}