/*
Title: Armstrong Number
Content:
Given an integer `n`, return `true` _if and only if it is an **Armstrong number**_.

The `k`\-digit number `n` is an Armstrong number if and only if the `kth` power of each digit sums to `n`.

**Example 1:**

**Input:** n = 153
**Output:** true
**Explanation:** 153 is a 3-digit number, and 153 = 13 + 53 + 33.

**Example 2:**

**Input:** n = 123
**Output:** false
**Explanation:** 123 is a 3-digit number, and 123 != 13 + 23 + 33 = 36.

**Constraints:**

*   `1 <= n <= 108`
*/

package com.example;
public class Solution99 {
    public boolean isArmstrong(int N) {
        int tmp = N, sum = 0;
        int k = String.valueOf(N).length();
        for (int i = 0; i < k; i++) {
            sum += Math.pow(tmp%10, k);
            tmp /= 10;
        }
        return sum == N;
    }
}