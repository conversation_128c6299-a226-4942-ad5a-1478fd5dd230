PROMPT_TEMPLATE_INIT=initial.ftl
PROMPT_TEMPLATE_INIT_SYSTEM=initial_system.ftl
PROMPT_TEMPLATE_EXTRA=extra.ftl
PROMPT_TEMPLATE_REPAIR=repair.ftl
PROMPT_TEMPLATE_TESTPILOT_INIT=initial_testpilot.ftl
PROMPT_TEMPLATE_SYMPROMPT_INIT_SYSTEM=initial_symprompt.ftl
PROMPT_TEMPLATE_SYMPROMPT_INIT=initial_symprompt_system.ftl
PROMPT_TEMPLATE_TELPA_INIT=initial_telpa.ftl
PROMPT_TEMPLATE_COVERUP_REPAIR=coverage.ftl
PROMPT_TEMPLATE_HITS_GEN_SLICE = hits_gen_slice.ftl
PROMPT_TEMPLATE_HITS_GEN = hits_gen.ftl
PROMPT_TEMPLATE_HITS_SYS_GEN = hits_system_gen.ftl
PROMPT_TEMPLATE_HITS_REPAIR = hits_repair.ftl
PROMPT_TEMPLATE_HITS_SYS_REPAIR = hits_system_repair.ftl
PROMPT_TEMPLATE_CHATTESTER_INIT = chattester_initial.ftl
PROMPT_TEMPLATE_CHATTESTER_EXTRA = chattester_extra.ftl
PROMPT_TEMPLATE_CHATTESTER_REPAIR = chattester_repair.ftl
PROMPT_TEMPLATE_TESTSPARK_INIT = testspark_initial.ftl
PROMPT_TEMPLATE_TESTSPARK_REPAIR = testspark_repair.ftl