#!/usr/bin/env python3
"""
科学严谨的 JMH 性能比较分析工具
用于比较 Copilot 生成代码与 Ground Truth 代码的性能差异

作者: 性能分析研究
日期: 2025-01-07
"""

import json
import os
import sys
import argparse
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class JMHPerformanceAnalyzer:
    """JMH 性能分析器"""

    def __init__(self):
        self.results = {}
        self.comparison_data = []

    def load_jmh_result(self, json_file):
        """加载单个 JMH 结果文件"""
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            return data[0] if isinstance(data, list) and len(data) > 0 else data
        except Exception as e:
            print(f"错误：无法加载文件 {json_file}: {e}")
            return None

    def extract_metrics(self, jmh_data):
        """提取关键性能指标"""
        if not jmh_data:
            return None

        metrics = {
            'test_name': jmh_data.get('benchmark', '').split('.')[-1],
            'mode': jmh_data.get('mode', ''),
            'threads': jmh_data.get('threads', 1),
            'forks': jmh_data.get('forks', 1),
            'warmup_iterations': jmh_data.get('warmupIterations', 0),
            'measurement_iterations': jmh_data.get('measurementIterations', 0),
        }

        # 主要性能指标
        primary = jmh_data.get('primaryMetric', {})
        metrics.update({
            'score': primary.get('score', 0),
            'score_error': primary.get('scoreError', 0),
            'score_unit': primary.get('scoreUnit', ''),
            'score_confidence_lower': primary.get('scoreConfidence', [0, 0])[0],
            'score_confidence_upper': primary.get('scoreConfidence', [0, 0])[1],
            'raw_data': primary.get('rawData', [])
        })

        # 百分位数
        percentiles = primary.get('scorePercentiles', {})
        for p in ['0.0', '50.0', '90.0', '95.0', '99.0', '100.0']:
            metrics[f'p{p.replace(".", "")}'] = percentiles.get(p, 0)

        # 次要指标（CPU、内存等）
        secondary = jmh_data.get('secondaryMetrics', {})

        # CPU 相关指标
        cpu_metrics = {}
        for key, value in secondary.items():
            if 'cpu' in key.lower() or 'cache' in key.lower() or 'instruction' in key.lower():
                metric_name = key.split('/')[-2] if '/' in key else key
                cpu_metrics[f'cpu_{metric_name}'] = value.get('score', 0)
        metrics.update(cpu_metrics)

        # GC 相关指标
        gc_metrics = {}
        for key, value in secondary.items():
            if 'gc' in key.lower() or 'alloc' in key.lower():
                metric_name = key.split('.')[-1] if '.' in key else key
                gc_metrics[f'gc_{metric_name}'] = value.get('score', 0)
        metrics.update(gc_metrics)

        return metrics

    def load_results_from_directories(self, ground_truth_dir, copilot_dir):
        """从目录加载所有结果"""
        print(f"加载 Ground Truth 结果从: {ground_truth_dir}")
        print(f"加载 Copilot 结果从: {copilot_dir}")

        gt_files = list(Path(ground_truth_dir).glob("*.json"))
        cp_files = list(Path(copilot_dir).glob("*.json"))

        print(f"找到 {len(gt_files)} 个 Ground Truth 文件")
        print(f"找到 {len(cp_files)} 个 Copilot 文件")

        # 匹配相同的测试
        matched_tests = []
        for gt_file in gt_files:
            test_name = gt_file.stem
            cp_file = Path(copilot_dir) / f"{test_name}.json"

            if cp_file.exists():
                gt_data = self.load_jmh_result(gt_file)
                cp_data = self.load_jmh_result(cp_file)

                if gt_data and cp_data:
                    gt_metrics = self.extract_metrics(gt_data)
                    cp_metrics = self.extract_metrics(cp_data)

                    if gt_metrics and cp_metrics:
                        matched_tests.append({
                            'test_name': test_name,
                            'ground_truth': gt_metrics,
                            'copilot': cp_metrics
                        })

        print(f"成功匹配 {len(matched_tests)} 个测试")
        return matched_tests

    def calculate_performance_ratio(self, gt_score, cp_score, mode):
        """计算性能比率"""
        if gt_score == 0:
            return float('inf') if cp_score > 0 else 1.0

        if mode == 'thrpt':  # 吞吐量模式，越高越好
            return cp_score / gt_score
        elif mode == 'avgt':  # 平均时间模式，越低越好
            return gt_score / cp_score
        else:
            return cp_score / gt_score

    def statistical_analysis(self, matched_tests):
        """统计分析"""
        if not matched_tests:
            print("没有匹配的测试数据进行分析")
            return None

        analysis_data = []

        for test in matched_tests:
            gt = test['ground_truth']
            cp = test['copilot']

            # 确保两个测试使用相同的模式
            if gt['mode'] != cp['mode']:
                print(f"警告：{test['test_name']} 的测试模式不匹配")
                continue

            ratio = self.calculate_performance_ratio(gt['score'], cp['score'], gt['mode'])

            analysis_data.append({
                'test_name': test['test_name'],
                'mode': gt['mode'],
                'gt_score': gt['score'],
                'cp_score': cp['score'],
                'performance_ratio': ratio,
                'gt_error': gt['score_error'],
                'cp_error': cp['score_error'],
                'gt_p50': gt['p500'],
                'cp_p50': cp['p500'],
                'gt_p95': gt['p950'],
                'cp_p95': cp['p950'],
                'improvement_pct': (ratio - 1) * 100
            })

        df = pd.DataFrame(analysis_data)

        if df.empty:
            print("没有有效的分析数据")
            return None

        # 统计摘要
        stats_summary = {
            'total_tests': len(df),
            'mean_ratio': df['performance_ratio'].mean(),
            'median_ratio': df['performance_ratio'].median(),
            'std_ratio': df['performance_ratio'].std(),
            'min_ratio': df['performance_ratio'].min(),
            'max_ratio': df['performance_ratio'].max(),
            'better_count': len(df[df['performance_ratio'] > 1]),
            'worse_count': len(df[df['performance_ratio'] < 1]),
            'similar_count': len(df[abs(df['performance_ratio'] - 1) < 0.05])
        }

        # 统计显著性检验
        if len(df) > 1:
            # Wilcoxon 符号秩检验
            ratios = df['performance_ratio'].values
            stat, p_value = stats.wilcoxon(ratios - 1, alternative='two-sided')
            stats_summary['wilcoxon_stat'] = stat
            stats_summary['wilcoxon_p_value'] = p_value
            stats_summary['significant'] = p_value < 0.05

        return df, stats_summary

    def generate_report(self, df, stats_summary, output_dir):
        """生成分析报告"""
        os.makedirs(output_dir, exist_ok=True)

        # 1. 文本报告
        report_file = os.path.join(output_dir, 'performance_analysis_report.txt')
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write("Copilot vs Ground Truth 性能比较分析报告\n")
            f.write("=" * 80 + "\n\n")

            f.write("1. 总体统计\n")
            f.write("-" * 40 + "\n")
            f.write(f"总测试数量: {stats_summary['total_tests']}\n")
            f.write(f"平均性能比率: {stats_summary['mean_ratio']:.4f}\n")
            f.write(f"中位数性能比率: {stats_summary['median_ratio']:.4f}\n")
            f.write(f"标准差: {stats_summary['std_ratio']:.4f}\n")
            f.write(f"最小比率: {stats_summary['min_ratio']:.4f}\n")
            f.write(f"最大比率: {stats_summary['max_ratio']:.4f}\n\n")

            f.write("2. 性能分布\n")
            f.write("-" * 40 + "\n")
            f.write(f"Copilot 更好的测试: {stats_summary['better_count']} ({stats_summary['better_count']/stats_summary['total_tests']*100:.1f}%)\n")
            f.write(f"Copilot 更差的测试: {stats_summary['worse_count']} ({stats_summary['worse_count']/stats_summary['total_tests']*100:.1f}%)\n")
            f.write(f"性能相似的测试: {stats_summary['similar_count']} ({stats_summary['similar_count']/stats_summary['total_tests']*100:.1f}%)\n\n")

            if 'wilcoxon_p_value' in stats_summary:
                f.write("3. 统计显著性检验\n")
                f.write("-" * 40 + "\n")
                f.write(f"Wilcoxon 符号秩检验 p-value: {stats_summary['wilcoxon_p_value']:.6f}\n")
                f.write(f"统计显著性 (p < 0.05): {'是' if stats_summary['significant'] else '否'}\n\n")

            f.write("4. 详细测试结果\n")
            f.write("-" * 40 + "\n")
            for _, row in df.iterrows():
                f.write(f"测试: {row['test_name']}\n")
                f.write(f"  Ground Truth: {row['gt_score']:.6f} ± {row['gt_error']:.6f} {row['mode']}\n")
                f.write(f"  Copilot:      {row['cp_score']:.6f} ± {row['cp_error']:.6f} {row['mode']}\n")
                f.write(f"  性能比率:     {row['performance_ratio']:.4f} ({row['improvement_pct']:+.1f}%)\n\n")

        print(f"分析报告已保存到: {report_file}")

        # 2. CSV 数据
        csv_file = os.path.join(output_dir, 'performance_comparison_data.csv')
        df.to_csv(csv_file, index=False, encoding='utf-8')
        print(f"详细数据已保存到: {csv_file}")

        # 3. 生成可视化图表
        self.create_visualizations(df, stats_summary, output_dir)

        return report_file, csv_file

    def create_visualizations(self, df, stats_summary, output_dir):
        """创建可视化图表"""
        plt.style.use('seaborn-v0_8')

        # 1. 性能比率分布直方图
        plt.figure(figsize=(12, 8))
        plt.subplot(2, 2, 1)
        plt.hist(df['performance_ratio'], bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        plt.axvline(x=1, color='red', linestyle='--', label='相等性能线')
        plt.axvline(x=df['performance_ratio'].mean(), color='green', linestyle='-', label=f'平均值: {df["performance_ratio"].mean():.3f}')
        plt.xlabel('性能比率 (Copilot/Ground Truth)')
        plt.ylabel('测试数量')
        plt.title('性能比率分布')
        plt.legend()
        plt.grid(True, alpha=0.3)

        # 2. 性能比较散点图
        plt.subplot(2, 2, 2)
        colors = ['red' if r < 1 else 'green' if r > 1 else 'gray' for r in df['performance_ratio']]
        plt.scatter(df['gt_score'], df['cp_score'], c=colors, alpha=0.6)

        # 添加对角线（相等性能线）
        min_val = min(df['gt_score'].min(), df['cp_score'].min())
        max_val = max(df['gt_score'].max(), df['cp_score'].max())
        plt.plot([min_val, max_val], [min_val, max_val], 'k--', alpha=0.5, label='相等性能线')

        plt.xlabel('Ground Truth 性能分数')
        plt.ylabel('Copilot 性能分数')
        plt.title('性能分数对比')
        plt.legend(['相等性能线', 'Copilot更差', 'Copilot更好', '相似'])
        plt.grid(True, alpha=0.3)

        # 3. 箱线图
        plt.subplot(2, 2, 3)
        data_to_plot = [df['gt_score'], df['cp_score']]
        plt.boxplot(data_to_plot, labels=['Ground Truth', 'Copilot'])
        plt.ylabel('性能分数')
        plt.title('性能分数分布对比')
        plt.grid(True, alpha=0.3)

        # 4. 改进百分比条形图
        plt.subplot(2, 2, 4)
        improvement_ranges = [
            ('< -50%', len(df[df['improvement_pct'] < -50])),
            ('-50% to -20%', len(df[(df['improvement_pct'] >= -50) & (df['improvement_pct'] < -20)])),
            ('-20% to -5%', len(df[(df['improvement_pct'] >= -20) & (df['improvement_pct'] < -5)])),
            ('-5% to +5%', len(df[(df['improvement_pct'] >= -5) & (df['improvement_pct'] <= 5)])),
            ('+5% to +20%', len(df[(df['improvement_pct'] > 5) & (df['improvement_pct'] <= 20)])),
            ('+20% to +50%', len(df[(df['improvement_pct'] > 20) & (df['improvement_pct'] <= 50)])),
            ('> +50%', len(df[df['improvement_pct'] > 50]))
        ]

        ranges, counts = zip(*improvement_ranges)
        colors = ['darkred', 'red', 'lightcoral', 'gray', 'lightgreen', 'green', 'darkgreen']
        plt.bar(range(len(ranges)), counts, color=colors)
        plt.xlabel('性能改进范围')
        plt.ylabel('测试数量')
        plt.title('性能改进分布')
        plt.xticks(range(len(ranges)), ranges, rotation=45)
        plt.grid(True, alpha=0.3)

        plt.tight_layout()
        plot_file = os.path.join(output_dir, 'performance_comparison_plots.png')
        plt.savefig(plot_file, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"可视化图表已保存到: {plot_file}")

        # 5. 详细的测试结果对比图
        if len(df) <= 20:  # 只有测试数量不太多时才生成
            plt.figure(figsize=(15, 8))
            x = range(len(df))
            width = 0.35

            plt.bar([i - width/2 for i in x], df['gt_score'], width, label='Ground Truth', alpha=0.8)
            plt.bar([i + width/2 for i in x], df['cp_score'], width, label='Copilot', alpha=0.8)

            plt.xlabel('测试')
            plt.ylabel('性能分数')
            plt.title('各测试性能详细对比')
            plt.xticks(x, df['test_name'], rotation=45, ha='right')
            plt.legend()
            plt.grid(True, alpha=0.3)

            detailed_plot_file = os.path.join(output_dir, 'detailed_test_comparison.png')
            plt.savefig(detailed_plot_file, dpi=300, bbox_inches='tight')
            plt.close()
            print(f"详细对比图已保存到: {detailed_plot_file}")

def main():
    parser = argparse.ArgumentParser(description='JMH 性能比较分析工具')
    parser.add_argument('--ground-truth', required=True, help='Ground Truth 结果目录')
    parser.add_argument('--copilot', required=True, help='Copilot 结果目录')
    parser.add_argument('--output', default='./analysis_results', help='输出目录')

    args = parser.parse_args()

    analyzer = JMHPerformanceAnalyzer()

    # 加载数据
    matched_tests = analyzer.load_results_from_directories(args.ground_truth, args.copilot)

    if not matched_tests:
        print("没有找到匹配的测试数据")
        return

    # 统计分析
    result = analyzer.statistical_analysis(matched_tests)
    if result is None:
        return

    df, stats_summary = result

    # 生成报告
    analyzer.generate_report(df, stats_summary, args.output)

    print("\n" + "=" * 60)
    print("分析完成！主要发现：")
    print(f"- 总共分析了 {stats_summary['total_tests']} 个测试")
    print(f"- 平均性能比率: {stats_summary['mean_ratio']:.4f}")
    print(f"- Copilot 表现更好: {stats_summary['better_count']} 个测试")
    print(f"- Copilot 表现更差: {stats_summary['worse_count']} 个测试")
    if 'significant' in stats_summary:
        print(f"- 统计显著性: {'显著' if stats_summary['significant'] else '不显著'}")

if __name__ == "__main__":
    main()
