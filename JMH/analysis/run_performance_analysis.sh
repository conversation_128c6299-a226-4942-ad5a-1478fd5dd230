#!/bin/bash

# JMH 性能比较分析运行脚本
# 用于比较 Copilot 生成代码与 Ground Truth 代码的性能

echo "JMH 性能比较分析工具"
echo "===================="

# 检查 Python 依赖
echo "检查 Python 依赖..."
python3 -c "import pandas, numpy, matplotlib, seaborn, scipy" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "错误：缺少必要的 Python 包"
    echo "请安装：pip install pandas numpy matplotlib seaborn scipy"
    exit 1
fi

# 设置默认路径
GROUND_TRUTH_DIR="/home/<USER>/math-conda/JMH/JMH-perfnorm/benchmark_results/20250509_155655_ground_truth"
COPILOT_DIR="/home/<USER>/math-conda/JMH/models/copilot/benchmark_results/20250707_175350_copilot"
OUTPUT_DIR="./performance_analysis_results"

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --ground-truth)
            GROUND_TRUTH_DIR="$2"
            shift 2
            ;;
        --copilot)
            COPILOT_DIR="$2"
            shift 2
            ;;
        --output)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        --help)
            echo "用法: $0 [选项]"
            echo ""
            echo "选项:"
            echo "  --ground-truth DIR  Ground Truth 结果目录"
            echo "  --copilot DIR       Copilot 结果目录"
            echo "  --output DIR        输出目录"
            echo "  --help              显示此帮助信息"
            echo ""
            echo "默认路径:"
            echo "  Ground Truth: $GROUND_TRUTH_DIR"
            echo "  Copilot:      $COPILOT_DIR"
            echo "  输出:         $OUTPUT_DIR"
            exit 0
            ;;
        *)
            echo "未知选项: $1"
            echo "使用 --help 查看帮助"
            exit 1
            ;;
    esac
done

# 检查目录是否存在
if [ ! -d "$GROUND_TRUTH_DIR" ]; then
    echo "错误：Ground Truth 目录不存在: $GROUND_TRUTH_DIR"
    exit 1
fi

if [ ! -d "$COPILOT_DIR" ]; then
    echo "错误：Copilot 目录不存在: $COPILOT_DIR"
    exit 1
fi

echo "分析配置:"
echo "  Ground Truth: $GROUND_TRUTH_DIR"
echo "  Copilot:      $COPILOT_DIR"
echo "  输出目录:     $OUTPUT_DIR"
echo ""

# 运行分析
echo "开始性能分析..."
python3 performance_comparison_analysis.py \
    --ground-truth "$GROUND_TRUTH_DIR" \
    --copilot "$COPILOT_DIR" \
    --output "$OUTPUT_DIR"

if [ $? -eq 0 ]; then
    echo ""
    echo "分析完成！结果已保存到: $OUTPUT_DIR"
    echo ""
    echo "生成的文件:"
    echo "  - performance_analysis_report.txt    # 详细分析报告"
    echo "  - performance_comparison_data.csv    # 原始数据"
    echo "  - performance_comparison_plots.png   # 可视化图表"
    echo "  - detailed_test_comparison.png       # 详细对比图（如果测试数量 ≤ 20）"
    echo ""
    echo "建议查看顺序:"
    echo "  1. 先阅读 performance_analysis_report.txt 了解总体情况"
    echo "  2. 查看 performance_comparison_plots.png 的可视化结果"
    echo "  3. 如需详细数据，查看 performance_comparison_data.csv"
else
    echo "分析失败，请检查错误信息"
    exit 1
fi
