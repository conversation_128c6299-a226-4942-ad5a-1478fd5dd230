#!/usr/bin/env python3
"""
快速 JMH 性能比较分析
专门用于比较您的 Copilot 和 Ground Truth 结果
"""

import json
import os
from pathlib import Path

def load_jmh_result(json_file):
    """加载 JMH 结果文件"""
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        return data[0] if isinstance(data, list) and len(data) > 0 else data
    except Exception as e:
        print(f"错误：无法加载文件 {json_file}: {e}")
        return None

def extract_key_metrics(jmh_data):
    """提取关键性能指标"""
    if not jmh_data:
        return None
    
    primary = jmh_data.get('primaryMetric', {})
    secondary = jmh_data.get('secondaryMetrics', {})
    
    metrics = {
        'test_name': jmh_data.get('benchmark', '').split('.')[-1],
        'mode': jmh_data.get('mode', ''),
        'score': primary.get('score', 0),
        'score_error': primary.get('scoreError', 0),
        'score_unit': primary.get('scoreUnit', ''),
        'forks': jmh_data.get('forks', 1),
        'iterations': jmh_data.get('measurementIterations', 0),
    }
    
    # 提取 CPU 指标
    cpu_instructions = 0
    cache_misses = 0
    
    for key, value in secondary.items():
        if 'instruction' in key.lower():
            cpu_instructions += value.get('score', 0)
        elif 'cache' in key.lower() and 'miss' in key.lower():
            cache_misses += value.get('score', 0)
    
    metrics['cpu_instructions'] = cpu_instructions
    metrics['cache_misses'] = cache_misses
    
    return metrics

def analyze_single_test():
    """分析单个测试的示例"""
    gt_file = "/home/<USER>/math-conda/JMH/JMH-perfnorm/benchmark_results/20250509_155655_ground_truth/Solution0_intToRoman_0_0_Test.json"
    cp_file = "/home/<USER>/math-conda/JMH/models/copilot/benchmark_results/20250707_175350_copilot/Solution0_intToRoman_0_0_Test.json"
    
    print("=" * 80)
    print("Solution0_intToRoman_0_0_Test 性能比较分析")
    print("=" * 80)
    
    # 加载数据
    gt_data = load_jmh_result(gt_file)
    cp_data = load_jmh_result(cp_file)
    
    if not gt_data or not cp_data:
        print("无法加载测试数据")
        return
    
    # 提取指标
    gt_metrics = extract_key_metrics(gt_data)
    cp_metrics = extract_key_metrics(cp_data)
    
    print(f"测试模式: {gt_metrics['mode']}")
    print(f"测试配置: {gt_metrics['forks']} forks, {gt_metrics['iterations']} iterations")
    print()
    
    # 主要性能指标对比
    print("1. 主要性能指标")
    print("-" * 40)
    print(f"Ground Truth: {gt_metrics['score']:.6f} ± {gt_metrics['score_error']:.6f} {gt_metrics['score_unit']}")
    print(f"Copilot:      {cp_metrics['score']:.6f} ± {cp_metrics['score_error']:.6f} {cp_metrics['score_unit']}")
    
    # 计算性能比率
    if gt_metrics['mode'] == 'thrpt':  # 吞吐量模式，越高越好
        ratio = cp_metrics['score'] / gt_metrics['score']
        improvement = (ratio - 1) * 100
    else:  # 平均时间模式，越低越好
        ratio = gt_metrics['score'] / cp_metrics['score']
        improvement = (ratio - 1) * 100
    
    print(f"性能比率:     {ratio:.4f}")
    print(f"性能变化:     {improvement:+.1f}%")
    
    if improvement > 5:
        print("结论:         Copilot 代码性能更好 ✓")
    elif improvement < -5:
        print("结论:         Copilot 代码性能更差 ✗")
    else:
        print("结论:         两者性能相似 ≈")
    
    print()
    
    # CPU 指标对比
    print("2. CPU 相关指标")
    print("-" * 40)
    if gt_metrics['cpu_instructions'] > 0 and cp_metrics['cpu_instructions'] > 0:
        inst_ratio = cp_metrics['cpu_instructions'] / gt_metrics['cpu_instructions']
        print(f"CPU 指令数:")
        print(f"  Ground Truth: {gt_metrics['cpu_instructions']:,.0f}")
        print(f"  Copilot:      {cp_metrics['cpu_instructions']:,.0f}")
        print(f"  比率:         {inst_ratio:.4f} ({(inst_ratio-1)*100:+.1f}%)")
    else:
        print("CPU 指令数据不可用")
    
    if gt_metrics['cache_misses'] > 0 and cp_metrics['cache_misses'] > 0:
        cache_ratio = cp_metrics['cache_misses'] / gt_metrics['cache_misses']
        print(f"缓存未命中:")
        print(f"  Ground Truth: {gt_metrics['cache_misses']:,.0f}")
        print(f"  Copilot:      {cp_metrics['cache_misses']:,.0f}")
        print(f"  比率:         {cache_ratio:.4f} ({(cache_ratio-1)*100:+.1f}%)")
    else:
        print("缓存数据不可用")
    
    print()
    
    # 详细的次要指标
    print("3. 详细性能计数器")
    print("-" * 40)
    
    gt_secondary = gt_data.get('secondaryMetrics', {})
    cp_secondary = cp_data.get('secondaryMetrics', {})
    
    # 找到共同的指标
    common_metrics = set(gt_secondary.keys()) & set(cp_secondary.keys())
    
    for metric in sorted(common_metrics):
        gt_score = gt_secondary[metric].get('score', 0)
        cp_score = cp_secondary[metric].get('score', 0)
        
        if gt_score > 0:
            ratio = cp_score / gt_score
            metric_name = metric.split('/')[-2] if '/' in metric else metric
            print(f"{metric_name:30s}: GT={gt_score:12.0f}, CP={cp_score:12.0f}, 比率={ratio:.3f}")
    
    print()
    print("=" * 80)

def batch_analysis():
    """批量分析所有匹配的测试"""
    gt_dir = "/home/<USER>/math-conda/JMH/JMH-perfnorm/benchmark_results/20250509_155655_ground_truth"
    cp_dir = "/home/<USER>/math-conda/JMH/models/copilot/benchmark_results/20250707_175350_copilot"
    
    print("批量性能分析")
    print("=" * 60)
    
    gt_files = list(Path(gt_dir).glob("*.json"))
    results = []
    
    for gt_file in gt_files:
        test_name = gt_file.stem
        cp_file = Path(cp_dir) / f"{test_name}.json"
        
        if cp_file.exists():
            gt_data = load_jmh_result(gt_file)
            cp_data = load_jmh_result(cp_file)
            
            if gt_data and cp_data:
                gt_metrics = extract_key_metrics(gt_data)
                cp_metrics = extract_key_metrics(cp_data)
                
                if gt_metrics and cp_metrics and gt_metrics['score'] > 0:
                    if gt_metrics['mode'] == 'thrpt':
                        ratio = cp_metrics['score'] / gt_metrics['score']
                    else:
                        ratio = gt_metrics['score'] / cp_metrics['score']
                    
                    improvement = (ratio - 1) * 100
                    
                    results.append({
                        'test': test_name,
                        'gt_score': gt_metrics['score'],
                        'cp_score': cp_metrics['score'],
                        'ratio': ratio,
                        'improvement': improvement,
                        'mode': gt_metrics['mode']
                    })
    
    if not results:
        print("没有找到匹配的测试结果")
        return
    
    # 排序并显示结果
    results.sort(key=lambda x: x['improvement'], reverse=True)
    
    print(f"找到 {len(results)} 个匹配的测试")
    print()
    print("性能改进排序 (正值表示 Copilot 更好):")
    print("-" * 100)
    print(f"{'测试名称':<50} {'GT分数':<12} {'CP分数':<12} {'比率':<8} {'改进%':<8} {'模式'}")
    print("-" * 100)
    
    better_count = 0
    worse_count = 0
    similar_count = 0
    
    for r in results:
        status = "✓" if r['improvement'] > 5 else "✗" if r['improvement'] < -5 else "≈"
        print(f"{r['test']:<50} {r['gt_score']:<12.6f} {r['cp_score']:<12.6f} {r['ratio']:<8.4f} {r['improvement']:<+7.1f}% {r['mode']} {status}")
        
        if r['improvement'] > 5:
            better_count += 1
        elif r['improvement'] < -5:
            worse_count += 1
        else:
            similar_count += 1
    
    print("-" * 100)
    print(f"总结: Copilot 更好={better_count}, 更差={worse_count}, 相似={similar_count}")
    
    # 统计摘要
    improvements = [r['improvement'] for r in results]
    avg_improvement = sum(improvements) / len(improvements)
    
    print(f"平均性能变化: {avg_improvement:+.1f}%")
    print(f"最大改进: {max(improvements):+.1f}%")
    print(f"最大退化: {min(improvements):+.1f}%")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--batch":
        batch_analysis()
    else:
        analyze_single_test()
        print()
        print("提示: 使用 --batch 参数进行批量分析")
