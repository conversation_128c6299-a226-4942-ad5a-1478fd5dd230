#!/usr/bin/env python3
"""
Research Report Generator for AI-Generated Code Performance Analysis
Generates publication-ready analysis suitable for software engineering journals
"""

import json
import numpy as np
from datetime import datetime
from JMH.analysis.test1.performance_metrics import AdvancedPerformanceAnalyzer
from typing import Dict, Any

class ResearchReportGenerator:
    """Generate comprehensive research report for publication"""
    
    def __init__(self, analyzer: AdvancedPerformanceAnalyzer):
        self.analyzer = analyzer
        self.report_data = None
        
    def generate_full_report(self, output_file: str = "performance_analysis_report.md"):
        """Generate complete research report"""
        self.report_data = self.analyzer.generate_comprehensive_report()
        
        report_content = self._generate_markdown_report()
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print(f"Research report generated: {output_file}")
        
        # Also generate JSON data for further analysis
        json_file = output_file.replace('.md', '_data.json')
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.report_data, f, indent=2, default=str)
        
        print(f"Raw data exported: {json_file}")
    
    def _generate_markdown_report(self) -> str:
        """Generate markdown formatted research report"""
        
        report = f"""# Advanced Performance Analysis of AI-Generated Code: A Multi-Dimensional Evaluation Framework

**Generated on:** {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

## Abstract

This report presents a comprehensive performance analysis of AI-generated code using novel multi-dimensional metrics that extend beyond traditional latency-based evaluations. We introduce five new performance indicators: Comprehensive Performance Index (CPI), Performance Stability Index (PSI), Architectural Efficiency Ratio (AER), Performance Dominance Score (PDS), and Efficiency@k, providing a holistic view of AI code generation quality suitable for software engineering research.

## 1. Introduction

Traditional performance evaluation of AI-generated code primarily focuses on execution time and correctness. However, modern software systems require comprehensive performance characterization that considers multiple dimensions including CPU efficiency, memory behavior, architectural utilization, and performance stability. This analysis introduces novel metrics addressing these requirements.

## 2. Methodology

### 2.1 Dataset Overview

- **Ground Truth Benchmarks:** {self.report_data['summary_statistics']['ground_truth_benchmarks']}
- **Generated Code Benchmarks:** {self.report_data['summary_statistics']['generated_benchmarks']}
- **Successfully Compiled & Tested:** {self.report_data['summary_statistics']['total_benchmarks']}
- **Success Rate:** {self.report_data['summary_statistics']['success_rate']:.3f} ({self.report_data['summary_statistics']['success_rate']*100:.1f}%)

### 2.2 Performance Metrics Framework

#### 2.2.1 Comprehensive Performance Index (CPI)
The CPI combines multiple performance dimensions with empirically-determined weights:
- Throughput (40%): Primary performance indicator
- CPU Efficiency (25%): Instructions per cycle ratio
- Memory Efficiency (20%): Inverse of GC allocation rate
- Cache Efficiency (10%): Cache hit ratio
- Branch Prediction Accuracy (5%): Branch prediction success rate

**Formula:**
```
CPI = 0.40 × (T_gen/T_gt) + 0.25 × (IPC_gen/IPC_gt) + 0.20 × (ME_gen/ME_gt) + 
      0.10 × (CE_gen/CE_gt) + 0.05 × (BPA_gen/BPA_gt)
```

#### 2.2.2 Performance Stability Index (PSI)
PSI measures performance consistency using coefficient of variation:
```
PSI = CV_generated / CV_ground_truth
```
where CV = σ/μ (coefficient of variation)

#### 2.2.3 Architectural Efficiency Ratio (AER)
AER evaluates hardware architecture utilization:
```
AER = 0.4 × (Cache_Eff_ratio) + 0.3 × (TLB_Eff_ratio) + 0.3 × (IPC_ratio)
```

#### 2.2.4 Performance Dominance Score (PDS)
PDS measures the fraction of performance dimensions where generated code outperforms ground truth:
```
PDS = (Number of dimensions where generated > ground_truth) / Total dimensions
```

#### 2.2.5 Efficiency@k
Extension of pass@k for performance evaluation:
```
Efficiency@k = P(at least one of k solutions achieves CPI > 1.0)
```

## 3. Results

### 3.1 Comprehensive Performance Index (CPI)

- **Mean CPI:** {self.report_data['comprehensive_performance_index']['mean']:.3f}
- **Median CPI:** {self.report_data['comprehensive_performance_index']['median']:.3f}
- **Standard Deviation:** {self.report_data['comprehensive_performance_index']['std']:.3f}
- **Better than Ground Truth:** {self.report_data['comprehensive_performance_index']['better_than_gt_ratio']:.3f} ({self.report_data['comprehensive_performance_index']['better_than_gt_ratio']*100:.1f}%)

{self._interpret_cpi_results()}

### 3.2 Performance Stability Index (PSI)

- **Mean PSI:** {self.report_data['performance_stability_index']['mean']:.3f}
- **More Stable than Ground Truth:** {self.report_data['performance_stability_index']['more_stable_ratio']:.3f} ({self.report_data['performance_stability_index']['more_stable_ratio']*100:.1f}%)

{self._interpret_psi_results()}

### 3.3 Architectural Efficiency Ratio (AER)

- **Mean AER:** {self.report_data['architectural_efficiency_ratio']['mean']:.3f}
- **Better Architecture Utilization:** {self.report_data['architectural_efficiency_ratio']['better_arch_utilization_ratio']:.3f} ({self.report_data['architectural_efficiency_ratio']['better_arch_utilization_ratio']*100:.1f}%)

{self._interpret_aer_results()}

### 3.4 Performance Dominance Score (PDS)

- **Mean Dominance:** {self.report_data['performance_dominance_score']['mean_dominance']:.3f}
- **Full Dominance Ratio:** {self.report_data['performance_dominance_score']['full_dominance_ratio']:.3f} ({self.report_data['performance_dominance_score']['full_dominance_ratio']*100:.1f}%)

{self._interpret_pds_results()}

### 3.5 Efficiency@k Analysis

{self._format_efficiency_at_k()}

## 4. Discussion

### 4.1 Key Findings

{self._generate_key_findings()}

### 4.2 Performance Characteristics

{self._analyze_performance_characteristics()}

### 4.3 Implications for AI Code Generation

{self._discuss_implications()}

## 5. Limitations

- The analysis is based on Java benchmarks and may not generalize to other programming languages
- Performance measurements are system-dependent and may vary across different hardware configurations
- The weighting scheme for CPI is empirically determined and may require adjustment for different domains

## 6. Conclusion

{self._generate_conclusion()}

## 7. Future Work

- Extension to other programming languages and domains
- Investigation of correlation between code complexity and performance metrics
- Development of predictive models for performance estimation
- Analysis of temporal performance trends in AI code generation

---

**Note:** This report is generated automatically from JMH benchmark results. For detailed raw data and statistical analysis, refer to the accompanying JSON data file.
"""
        
        return report
    
    def _interpret_cpi_results(self) -> str:
        """Interpret CPI results"""
        mean_cpi = self.report_data['comprehensive_performance_index']['mean']
        better_ratio = self.report_data['comprehensive_performance_index']['better_than_gt_ratio']
        
        if mean_cpi > 1.0:
            performance_assessment = "superior"
        elif mean_cpi > 0.8:
            performance_assessment = "competitive"
        else:
            performance_assessment = "suboptimal"
        
        return f"""The CPI analysis reveals {performance_assessment} performance of AI-generated code compared to ground truth implementations. With {better_ratio*100:.1f}% of benchmarks achieving CPI > 1.0, the generated code demonstrates {'strong' if better_ratio > 0.5 else 'moderate' if better_ratio > 0.3 else 'limited'} performance advantages across multiple dimensions."""
    
    def _interpret_psi_results(self) -> str:
        """Interpret PSI results"""
        mean_psi = self.report_data['performance_stability_index']['mean']
        stable_ratio = self.report_data['performance_stability_index']['more_stable_ratio']
        
        stability_assessment = "more stable" if mean_psi < 1.0 else "less stable"
        
        return f"""Performance stability analysis indicates that AI-generated code is {stability_assessment} than ground truth implementations on average. {stable_ratio*100:.1f}% of benchmarks show improved stability (PSI < 1.0), suggesting {'consistent' if stable_ratio > 0.5 else 'variable'} performance predictability."""
    
    def _interpret_aer_results(self) -> str:
        """Interpret AER results"""
        mean_aer = self.report_data['architectural_efficiency_ratio']['mean']
        better_arch_ratio = self.report_data['architectural_efficiency_ratio']['better_arch_utilization_ratio']
        
        arch_assessment = "superior" if mean_aer > 1.0 else "comparable" if mean_aer > 0.9 else "inferior"
        
        return f"""Architectural efficiency analysis shows {arch_assessment} hardware utilization by AI-generated code. {better_arch_ratio*100:.1f}% of benchmarks demonstrate improved architectural efficiency, indicating {'effective' if better_arch_ratio > 0.4 else 'moderate' if better_arch_ratio > 0.2 else 'limited'} optimization of cache hierarchy, TLB, and pipeline resources."""
    
    def _interpret_pds_results(self) -> str:
        """Interpret PDS results"""
        mean_dominance = self.report_data['performance_dominance_score']['mean_dominance']
        full_dominance = self.report_data['performance_dominance_score']['full_dominance_ratio']
        
        return f"""Multi-dimensional performance analysis reveals that AI-generated code achieves dominance in {mean_dominance*100:.1f}% of performance dimensions on average. {full_dominance*100:.1f}% of benchmarks show complete dominance across all measured dimensions, indicating {'comprehensive' if full_dominance > 0.2 else 'selective' if full_dominance > 0.05 else 'limited'} performance superiority."""
    
    def _format_efficiency_at_k(self) -> str:
        """Format Efficiency@k results"""
        efficiency_k = self.report_data['efficiency_at_k']
        
        result = ""
        for k, efficiency in efficiency_k.items():
            result += f"- **Efficiency@{k}:** {efficiency:.3f} ({efficiency*100:.1f}%)\n"
        
        result += f"\nThe Efficiency@k analysis demonstrates {'strong' if efficiency_k[1] > 0.5 else 'moderate' if efficiency_k[1] > 0.3 else 'limited'} potential for generating high-performance solutions."
        
        return result
    
    def _generate_key_findings(self) -> str:
        """Generate key findings section"""
        findings = []
        
        # CPI finding
        cpi_mean = self.report_data['comprehensive_performance_index']['mean']
        if cpi_mean > 1.0:
            findings.append(f"AI-generated code achieves {cpi_mean:.1f}× comprehensive performance compared to ground truth")
        else:
            findings.append(f"AI-generated code achieves {cpi_mean:.1f}× comprehensive performance, indicating room for improvement")
        
        # Success rate finding
        success_rate = self.report_data['summary_statistics']['success_rate']
        findings.append(f"Code generation success rate of {success_rate*100:.1f}% demonstrates {'high' if success_rate > 0.8 else 'moderate' if success_rate > 0.6 else 'limited'} reliability")
        
        # Stability finding
        psi_mean = self.report_data['performance_stability_index']['mean']
        if psi_mean < 1.0:
            findings.append("Generated code exhibits superior performance stability compared to ground truth")
        else:
            findings.append("Performance stability requires improvement in AI-generated solutions")
        
        return "\n".join(f"- {finding}" for finding in findings)
    
    def _analyze_performance_characteristics(self) -> str:
        """Analyze performance characteristics"""
        return """The multi-dimensional analysis reveals distinct performance patterns:

1. **Throughput vs. Efficiency Trade-offs:** Generated code may optimize for different performance aspects than ground truth implementations
2. **Architectural Awareness:** Variable effectiveness in utilizing modern CPU features suggests opportunities for architecture-aware code generation
3. **Stability Patterns:** Performance consistency varies significantly across different problem domains and complexity levels"""
    
    def _discuss_implications(self) -> str:
        """Discuss implications for AI code generation"""
        return """These findings have several implications for AI code generation research:

1. **Multi-dimensional Optimization:** Future AI models should consider comprehensive performance metrics rather than single-objective optimization
2. **Architecture-Aware Training:** Incorporating hardware performance counters in training data could improve architectural efficiency
3. **Stability-Performance Balance:** Developing techniques to achieve both high performance and consistent behavior across different inputs"""
    
    def _generate_conclusion(self) -> str:
        """Generate conclusion"""
        cpi_mean = self.report_data['comprehensive_performance_index']['mean']
        success_rate = self.report_data['summary_statistics']['success_rate']
        
        performance_level = "competitive" if cpi_mean > 0.8 else "developing"
        
        return f"""This comprehensive analysis demonstrates that current AI code generation achieves {performance_level} performance across multiple dimensions. With a {success_rate*100:.1f}% success rate and mean CPI of {cpi_mean:.3f}, there are clear opportunities for improvement in both reliability and performance optimization. The proposed multi-dimensional evaluation framework provides valuable insights for advancing AI code generation research and development."""


if __name__ == "__main__":
    # Create analyzer and report generator
    analyzer = AdvancedPerformanceAnalyzer(
        ground_truth_dir="../JMH-perfnorm/benchmark_results/20250509_155655_ground_truth",
        generated_code_dir="../models/copilot/benchmark_results/20250707_175350_copilot"
    )
    
    analyzer.load_benchmark_data()
    
    report_generator = ResearchReportGenerator(analyzer)
    report_generator.generate_full_report("ai_code_performance_analysis.md")
    
    print("Research report generated successfully!")
