#!/usr/bin/env python3
"""
数据对比分析：解释为什么两个脚本结果不同
"""

import numpy as np
from JMH.analysis.test1.performance_metrics_copilot import AdvancedPerformanceAnalyzer

def analyze_data_differences():
    """分析真实数据与示例数据的差异"""
    
    print("="*80)
    print("数据差异分析：为什么两个脚本结果不同")
    print("="*80)
    
    # 1. 加载真实数据
    print("\n【1. 真实数据分析】")
    analyzer = AdvancedPerformanceAnalyzer(
        ground_truth_dir="../../JMH-perfnorm/benchmark_results/20250509_155655_ground_truth",
        generated_code_dir="../../models/copilot/benchmark_results/20250707_175350_copilot"
    )
    analyzer.load_benchmark_data()
    
    # 计算真实数据的指标
    real_cpi = analyzer.calculate_comprehensive_performance_index()
    real_psi = analyzer.calculate_performance_stability_index()
    real_aer = analyzer.calculate_architectural_efficiency_ratio()
    
    print(f"数据集规模: {len(analyzer.common_benchmarks)} 个基准测试")
    print(f"CPI 分布:")
    print(f"  - 平均值: {np.mean(list(real_cpi.values())):.3f}")
    print(f"  - 中位数: {np.median(list(real_cpi.values())):.3f}")
    print(f"  - 最小值: {np.min(list(real_cpi.values())):.3f}")
    print(f"  - 最大值: {np.max(list(real_cpi.values())):.3f}")
    print(f"  - 标准差: {np.std(list(real_cpi.values())):.3f}")
    
    # 2. 示例数据分析
    print("\n【2. 示例数据分析】")
    print("示例数据特点:")
    print("  - 数据集规模: 1 个硬编码示例")
    print("  - 基准吞吐量: 0.386 ops/ms")
    print("  - 生成代码吞吐量: 0.333 ops/ms")
    print("  - 吞吐量比: 0.863 (生成代码较慢)")
    print("  - 内存分配率比: 0.983 (生成代码稍差)")
    print("  - 其他指标比: 1.000 (完全相同)")
    
    # 3. 差异原因分析
    print("\n【3. 差异原因分析】")
    print("造成结果不同的主要原因:")
    print("  1. 数据范围: 150个真实测试 vs 1个示例")
    print("  2. 数据质量: 真实JMH结果 vs 演示用模拟数据")
    print("  3. 统计效应: 大样本平均 vs 单点数据")
    print("  4. 代表性: 全面覆盖 vs 特定案例")
    
    # 4. 找一个真实的低性能案例
    print("\n【4. 真实数据中的性能分布】")
    cpi_values = list(real_cpi.values())
    low_performance_count = sum(1 for cpi in cpi_values if cpi < 1.0)
    high_performance_count = sum(1 for cpi in cpi_values if cpi > 1.0)
    
    print(f"性能分布:")
    print(f"  - CPI < 1.0 (性能较差): {low_performance_count} 个 ({low_performance_count/len(cpi_values)*100:.1f}%)")
    print(f"  - CPI > 1.0 (性能较好): {high_performance_count} 个 ({high_performance_count/len(cpi_values)*100:.1f}%)")
    
    # 找到最差和最好的案例
    min_cpi_idx = np.argmin(cpi_values)
    max_cpi_idx = np.argmax(cpi_values)
    benchmark_names = list(real_cpi.keys())
    
    print(f"\n极端案例:")
    print(f"  - 最差性能: {benchmark_names[min_cpi_idx]} (CPI = {cpi_values[min_cpi_idx]:.3f})")
    print(f"  - 最佳性能: {benchmark_names[max_cpi_idx]} (CPI = {cpi_values[max_cpi_idx]:.3f})")
    
    # 5. 示例数据的代表性
    print("\n【5. 示例数据的代表性】")
    example_cpi = 0.942
    percentile = (sum(1 for cpi in cpi_values if cpi < example_cpi) / len(cpi_values)) * 100
    print(f"示例CPI (0.942) 在真实数据中的位置:")
    print(f"  - 百分位数: {percentile:.1f}% (即有{percentile:.1f}%的测试性能更差)")
    print(f"  - 这个示例代表了性能较差的情况")
    
    # 6. 建议
    print("\n【6. 使用建议】")
    print("两个脚本的不同用途:")
    print("  - performance_metrics.py: 用于真实的大规模性能分析")
    print("  - metrics_calculation_example.py: 用于理解指标计算过程")
    print("  - 研究发表: 应使用 performance_metrics.py 的结果")
    print("  - 教学演示: 可使用 metrics_calculation_example.py")
    
    return {
        'real_cpi_mean': np.mean(list(real_cpi.values())),
        'real_cpi_std': np.std(list(real_cpi.values())),
        'example_cpi': example_cpi,
        'example_percentile': percentile
    }

if __name__ == "__main__":
    results = analyze_data_differences()
    
    print(f"\n" + "="*80)
    print("总结")
    print("="*80)
    print(f"真实数据平均CPI: {results['real_cpi_mean']:.3f} ± {results['real_cpi_std']:.3f}")
    print(f"示例数据CPI: {results['example_cpi']:.3f}")
    print(f"示例数据代表了第 {results['example_percentile']:.1f} 百分位的性能水平")
    print("\n结论: 两个结果不同是正常的，因为它们分析的数据范围和目的不同。")
