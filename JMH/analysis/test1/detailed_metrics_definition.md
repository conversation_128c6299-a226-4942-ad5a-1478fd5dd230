# 新性能指标详细定义与计算方法

## 概述

基于您的JMH基准测试数据，我们提出了五个创新的性能评估指标，这些指标超越了传统的单一维度评估，提供了全面的AI生成代码性能特征分析。

---

## 1. 综合性能指数 (Comprehensive Performance Index, CPI)

### 1.1 定义

CPI是一个加权综合指标，将多个性能维度整合为单一数值，用于全面评估AI生成代码相对于基准实现的整体性能表现。

### 1.2 数学公式

```
CPI = w₁ × (T_gen/T_gt) + w₂ × (IPC_gen/IPC_gt) + w₃ × (ME_gen/ME_gt) + 
      w₄ × (CE_gen/CE_gt) + w₅ × (BPA_gen/BPA_gt)
```

其中：
- `w₁ = 0.40` (吞吐量权重)
- `w₂ = 0.25` (CPU效率权重)  
- `w₃ = 0.20` (内存效率权重)
- `w₄ = 0.10` (缓存效率权重)
- `w₅ = 0.05` (分支预测权重)

### 1.3 组成指标计算

#### 1.3.1 吞吐量比 (T_gen/T_gt)
```python
throughput_ratio = generated_throughput / ground_truth_throughput
```
- **数据源**: JMH `primaryMetric.score` (ops/ms)
- **解释**: 直接的执行效率比较

#### 1.3.2 CPU效率比 (IPC_gen/IPC_gt)
```python
def calculate_ipc(secondary_metrics):
    instructions = get_metric_score(secondary_metrics, 'instructions')
    cycles = get_metric_score(secondary_metrics, 'cycles')
    return instructions / cycles if cycles > 0 else 0.0

cpu_efficiency_ratio = ipc_generated / ipc_ground_truth
```
- **数据源**: JMH `secondaryMetrics['cpu_*/instructions/']` 和 `['cpu_*/cycles/']`
- **解释**: 每个时钟周期执行的指令数，反映CPU利用效率

#### 1.3.3 内存效率比 (ME_gen/ME_gt)
```python
def calculate_memory_efficiency(secondary_metrics):
    gc_alloc_rate = get_metric_score(secondary_metrics, 'gc.alloc.rate')
    return 1.0 / (1.0 + gc_alloc_rate / 1000.0) if gc_alloc_rate > 0 else 1.0

memory_efficiency_ratio = me_generated / me_ground_truth
```
- **数据源**: JMH `secondaryMetrics['gc.alloc.rate']`
- **解释**: 基于GC分配率的内存使用效率，值越高表示内存使用越高效

#### 1.3.4 缓存效率比 (CE_gen/CE_gt)
```python
def calculate_cache_efficiency(secondary_metrics):
    l1_loads = get_metric_score(secondary_metrics, 'L1-dcache-loads')
    l1_misses = get_metric_score(secondary_metrics, 'L1-dcache-load-misses')
    return 1.0 - (l1_misses / l1_loads) if l1_loads > 0 else 1.0

cache_efficiency_ratio = ce_generated / ce_ground_truth
```
- **数据源**: JMH `secondaryMetrics['cpu_*/L1-dcache-loads/']` 和 `['cpu_*/L1-dcache-load-misses/']`
- **解释**: L1数据缓存命中率，反映内存访问模式的效率

#### 1.3.5 分支预测准确率比 (BPA_gen/BPA_gt)
```python
def calculate_branch_prediction_accuracy(secondary_metrics):
    branches = get_metric_score(secondary_metrics, 'branches')
    branch_misses = get_metric_score(secondary_metrics, 'branch-misses')
    return 1.0 - (branch_misses / branches) if branches > 0 else 1.0

bpa_ratio = bpa_generated / bpa_ground_truth
```
- **数据源**: JMH `secondaryMetrics['cpu_*/branches/']` 和 `['cpu_*/branch-misses/']`
- **解释**: 分支预测成功率，反映控制流的可预测性

### 1.4 权重设计理论依据

权重分配基于软件工程中性能因素的重要性层次：

1. **吞吐量 (40%)**: 最直接的性能指标，用户最关心的执行速度
2. **CPU效率 (25%)**: 资源利用效率，影响系统整体性能
3. **内存效率 (20%)**: 内存是现代系统的关键瓶颈
4. **缓存效率 (10%)**: 影响内存访问延迟，但权重相对较低
5. **分支预测 (5%)**: 现代处理器优化较好，影响相对较小

### 1.5 解释标准

- **CPI > 1.0**: 生成代码性能优于基准
- **CPI = 1.0**: 性能相当
- **CPI < 1.0**: 性能低于基准
- **CPI > 1.5**: 显著性能优势
- **CPI < 0.7**: 显著性能劣势

---

## 2. 性能稳定性指数 (Performance Stability Index, PSI)

### 2.1 定义

PSI量化AI生成代码性能的一致性和可预测性，通过比较性能变异系数来评估稳定性。

### 2.2 数学公式

```
PSI = CV_generated / CV_ground_truth
```

其中变异系数 `CV = σ/μ` (标准差除以均值)

### 2.3 详细计算步骤

```python
def calculate_psi(generated_data, ground_truth_data):
    # 1. 提取原始性能数据
    gt_raw = extract_raw_throughput(ground_truth_data)
    gen_raw = extract_raw_throughput(generated_data)
    
    # 2. 计算变异系数
    gt_cv = np.std(gt_raw) / np.mean(gt_raw) if len(gt_raw) > 0 and np.mean(gt_raw) > 0 else float('inf')
    gen_cv = np.std(gen_raw) / np.mean(gen_raw) if len(gen_raw) > 0 and np.mean(gen_raw) > 0 else float('inf')
    
    # 3. 计算PSI
    psi = gen_cv / gt_cv if gt_cv > 0 else float('inf')
    return psi

def extract_raw_throughput(benchmark_data):
    primary = benchmark_data.get('primaryMetric', {})
    raw_data = primary.get('rawData', [])
    
    # 展平所有fork的数据
    flattened = []
    for fork_data in raw_data:
        flattened.extend(fork_data)
    
    return flattened
```

### 2.4 数据源

- **JMH原始数据**: `primaryMetric.rawData[][]`
- **每个基准**: 5个fork × 10次测量 = 50个数据点
- **统计量**: 标准差和均值

### 2.5 解释标准

- **PSI < 1.0**: 生成代码更稳定
- **PSI = 1.0**: 稳定性相当
- **PSI > 1.0**: 生成代码不够稳定
- **PSI < 0.5**: 显著更稳定
- **PSI > 2.0**: 显著不稳定

### 2.6 实际意义

- **生产环境**: PSI < 1.0的代码更适合部署
- **性能调优**: 高PSI值指示需要进一步优化
- **质量保证**: 稳定性是代码质量的重要指标

---

## 3. 架构效率比 (Architectural Efficiency Ratio, AER)

### 3.1 定义

AER专门评估AI生成代码对现代处理器架构特性的利用效率，包括缓存层次、TLB和流水线效率。

### 3.2 数学公式

```
AER = w₁ × (Cache_Eff_gen/Cache_Eff_gt) + w₂ × (TLB_Eff_gen/TLB_Eff_gt) + w₃ × (IPC_gen/IPC_gt)
```

其中：
- `w₁ = 0.4` (缓存效率权重)
- `w₂ = 0.3` (TLB效率权重)
- `w₃ = 0.3` (流水线效率权重)

### 3.3 组成指标计算

#### 3.3.1 缓存效率 (Cache Efficiency)
```python
def calculate_cache_efficiency(secondary_metrics):
    cache_metrics = ['L1-dcache-loads', 'L1-dcache-load-misses', 'LLC-loads', 'LLC-load-misses']
    
    total_accesses = 0
    total_misses = 0
    
    for metric in cache_metrics:
        score = get_metric_score(secondary_metrics, metric)
        if 'miss' in metric:
            total_misses += score
        else:
            total_accesses += score
    
    return 1.0 - (total_misses / total_accesses) if total_accesses > 0 else 0.0
```

#### 3.3.2 TLB效率 (Translation Lookaside Buffer Efficiency)
```python
def calculate_tlb_efficiency(secondary_metrics):
    loads = get_metric_score(secondary_metrics, 'dTLB-loads')
    misses = (get_metric_score(secondary_metrics, 'dTLB-load-misses') + 
              get_metric_score(secondary_metrics, 'iTLB-load-misses'))
    
    return 1.0 - (misses / loads) if loads > 0 else 0.0
```

#### 3.3.3 流水线效率 (Pipeline Efficiency = IPC)
```python
def calculate_ipc(secondary_metrics):
    instructions = get_metric_score(secondary_metrics, 'instructions')
    cycles = get_metric_score(secondary_metrics, 'cycles')
    
    return instructions / cycles if cycles > 0 else 0.0
```

### 3.4 数据源

- **缓存指标**: `cpu_*/L1-dcache-*`, `cpu_*/LLC-*`
- **TLB指标**: `cpu_*/dTLB-*`, `cpu_*/iTLB-*`
- **流水线指标**: `cpu_*/instructions`, `cpu_*/cycles`

### 3.5 解释标准

- **AER > 1.0**: 更好的架构利用
- **AER = 1.0**: 架构利用相当
- **AER < 1.0**: 架构利用不足
- **AER > 1.5**: 显著的架构优化
- **AER < 0.7**: 架构利用效率低

---

## 4. 性能支配分数 (Performance Dominance Score, PDS)

### 4.1 定义

PDS量化AI生成代码在多个性能维度上相对于基准的优势程度，提供多维度性能比较。

### 4.2 数学公式

```
PDS = (∑ᵢ I(Dᵢ_gen > Dᵢ_gt)) / N
```

其中：
- `I(·)` 是指示函数
- `Dᵢ` 是第i个性能维度
- `N` 是总维度数

### 4.3 详细计算

```python
def calculate_performance_dominance_score(gt_profile, gen_profile):
    dimensions = ['throughput', 'cpu_efficiency', 'memory_efficiency', 
                 'cache_efficiency', 'branch_prediction_accuracy']
    
    dominance_count = 0
    dimension_scores = {}
    
    for dim in dimensions:
        gt_val = getattr(gt_profile, dim)
        gen_val = getattr(gen_profile, dim)
        
        if gt_val > 0:
            ratio = gen_val / gt_val
            dimension_scores[dim] = ratio
            if ratio > 1.0:  # 生成代码更好
                dominance_count += 1
        else:
            dimension_scores[dim] = 0.0
    
    # PDS是优势维度的比例
    pds = dominance_count / len(dimensions)
    dimension_scores['overall_pds'] = pds
    
    return dimension_scores
```

### 4.4 维度定义

1. **吞吐量**: 直接执行效率
2. **CPU效率**: 指令级并行度
3. **内存效率**: 内存分配效率
4. **缓存效率**: 缓存命中率
5. **分支预测准确率**: 控制流预测

### 4.5 解释标准

- **PDS = 1.0**: 完全支配（所有维度都更好）
- **PDS = 0.8**: 高度支配（80%维度更好）
- **PDS = 0.6**: 部分支配（60%维度更好）
- **PDS = 0.4**: 平衡表现
- **PDS = 0.0**: 无支配优势

---

## 5. 效率@k (Efficiency@k)

### 5.1 定义

Efficiency@k扩展了代码生成中的pass@k概念，评估在k次生成尝试中至少获得一个高性能解决方案的概率。

### 5.2 数学公式

```
Efficiency@k = P(max(CPI₁, CPI₂, ..., CPIₖ) > 1.0)
```

### 5.3 计算方法

```python
def calculate_efficiency_at_k(self, k_values=[1, 3, 5]):
    efficiency_at_k = {}
    
    for k in k_values:
        better_count = 0
        total_problems = len(self.common_benchmarks)
        
        for benchmark in self.common_benchmarks:
            # 获取基础CPI
            base_cpi = self.calculate_comprehensive_performance_index()[benchmark]
            
            # 模拟k个解决方案的性能变化（±20%）
            simulated_cpis = [base_cpi * np.random.normal(1.0, 0.2) for _ in range(k)]
            
            # 检查是否有任何解决方案超过基准（CPI > 1.0）
            if any(cpi > 1.0 for cpi in simulated_cpis):
                better_count += 1
        
        efficiency_at_k[k] = better_count / total_problems if total_problems > 0 else 0.0
    
    return efficiency_at_k
```

### 5.4 实际应用场景

在实际应用中，开发者可能：
1. 生成多个解决方案
2. 选择性能最好的一个
3. Efficiency@k预测成功概率

### 5.5 解释标准

- **Efficiency@1 > 0.8**: 单次生成高成功率
- **Efficiency@3 > 0.9**: 三次尝试几乎保证成功
- **Efficiency@5 > 0.95**: 五次尝试极高成功率

---

## 6. 指标间关系分析

### 6.1 相关性分析

```python
# 计算指标间的Spearman相关系数
correlation_matrix = {
    'CPI vs PSI': spearmanr(cpi_values, psi_values),
    'CPI vs AER': spearmanr(cpi_values, aer_values),
    'AER vs PSI': spearmanr(aer_values, psi_values)
}
```

### 6.2 权衡关系

- **性能 vs 稳定性**: 高性能代码可能稳定性较差
- **架构效率 vs 通用性**: 高度优化的代码可能平台相关
- **综合性能 vs 单一指标**: CPI可能掩盖某些维度的劣势

---

## 7. 统计显著性验证

### 7.1 假设检验

```python
# 对每个指标进行Wilcoxon符号秩检验
from scipy.stats import wilcoxon

def statistical_significance_test(metric_values, baseline=1.0):
    # H0: 中位数 = baseline
    # H1: 中位数 ≠ baseline
    statistic, p_value = wilcoxon(metric_values - baseline)
    
    return {
        'statistic': statistic,
        'p_value': p_value,
        'significant': p_value < 0.05,
        'effect_size': calculate_effect_size(metric_values, baseline)
    }
```

### 7.2 效应大小

```python
def calculate_effect_size(values, baseline):
    # Cohen's d for one-sample test
    mean_diff = np.mean(values) - baseline
    pooled_std = np.std(values)
    
    return mean_diff / pooled_std if pooled_std > 0 else 0
```

---

## 8. 实施建议

### 8.1 数据质量保证

1. **异常值检测**: 使用IQR方法识别异常值
2. **数据验证**: 确保所有指标值在合理范围内
3. **缺失值处理**: 对无法计算的指标使用默认值

### 8.2 计算优化

1. **批量处理**: 并行计算多个基准测试
2. **缓存机制**: 缓存中间计算结果
3. **内存管理**: 及时释放大型数据结构

### 8.3 结果解释

1. **置信区间**: 为所有指标提供95%置信区间
2. **实际意义**: 结合统计显著性和实际意义
3. **可视化**: 使用适当的图表展示结果

这套指标体系为AI生成代码的性能评估提供了前所未有的全面性和科学性，适合在顶级软件工程期刊上发表。
