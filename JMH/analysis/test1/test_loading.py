#!/usr/bin/env python3
"""
Test script to debug data loading issues
"""

import json
import os
from pathlib import Path

def test_loading():
    ground_truth_dir = Path("../JMH-perfnorm/benchmark_results/20250509_155655_ground_truth")
    generated_code_dir = Path("../models/copilot/benchmark_results/20250707_175350_copilot")
    
    print(f"Ground truth dir exists: {ground_truth_dir.exists()}")
    print(f"Generated code dir exists: {generated_code_dir.exists()}")
    
    # Test loading ground truth
    gt_files = list(ground_truth_dir.glob("*.json"))
    print(f"Ground truth JSON files found: {len(gt_files)}")
    
    if gt_files:
        print(f"First GT file: {gt_files[0]}")
        try:
            with open(gt_files[0], 'r') as f:
                data = json.load(f)
                print(f"GT file loaded successfully, data length: {len(data)}")
                if data:
                    print(f"First entry keys: {list(data[0].keys())}")
        except Exception as e:
            print(f"Error loading GT file: {e}")
    
    # Test loading generated code
    gen_files = list(generated_code_dir.glob("*.json"))
    print(f"Generated code JSON files found: {len(gen_files)}")
    
    if gen_files:
        print(f"First Gen file: {gen_files[0]}")
        try:
            with open(gen_files[0], 'r') as f:
                data = json.load(f)
                print(f"Gen file loaded successfully, data length: {len(data)}")
                if data:
                    print(f"First entry keys: {list(data[0].keys())}")
        except Exception as e:
            print(f"Error loading Gen file: {e}")
    
    # Find common files
    gt_names = {f.stem for f in gt_files}
    gen_names = {f.stem for f in gen_files}
    common = gt_names.intersection(gen_names)
    print(f"Common benchmark names: {len(common)}")
    if common:
        print(f"Sample common names: {list(common)[:5]}")

if __name__ == "__main__":
    test_loading()
