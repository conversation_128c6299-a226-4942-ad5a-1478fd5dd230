#!/usr/bin/env python3
"""
Visualization module for advanced performance metrics
Generates publication-quality plots for software engineering journals
"""

import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
import pandas as pd
from typing import Dict, List
import matplotlib.patches as mpatches
from performance_metrics import AdvancedPerformanceAnalyzer

# Set publication-quality style
plt.style.use('seaborn-v0_8-whitegrid')
sns.set_palette("husl")

class PerformanceVisualizer:
    """Generate publication-quality visualizations for performance analysis"""

    def __init__(self, analyzer: AdvancedPerformanceAnalyzer):
        self.analyzer = analyzer
        self.report = None

    def generate_all_plots(self, output_dir: str = "plots"):
        """Generate all visualization plots"""
        import os
        os.makedirs(output_dir, exist_ok=True)

        # Generate comprehensive report
        self.report = self.analyzer.generate_comprehensive_report()

        # Generate individual plots
        self.plot_performance_overview(f"{output_dir}/performance_overview.png")
        self.plot_metric_comparison(f"{output_dir}/metric_comparison.png")
        self.plot_dominance_analysis(f"{output_dir}/dominance_analysis.png")
        self.plot_efficiency_at_k(f"{output_dir}/efficiency_at_k.png")
        self.plot_stability_analysis(f"{output_dir}/stability_analysis.png")

        print(f"All plots saved to {output_dir}/")

    def plot_performance_overview(self, filename: str):
        """Create comprehensive performance overview plot"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))

        # CPI Distribution
        cpi_values = list(self.report['detailed_results']['cpi'].values())
        ax1.hist(cpi_values, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        ax1.axvline(x=1.0, color='red', linestyle='--', linewidth=2, label='Ground Truth Baseline')
        ax1.axvline(x=np.mean(cpi_values), color='orange', linestyle='-', linewidth=2, label=f'Mean CPI: {np.mean(cpi_values):.3f}')
        ax1.set_xlabel('Comprehensive Performance Index (CPI)')
        ax1.set_ylabel('Frequency')
        ax1.set_title('CPI Distribution')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # PSI vs CPI Scatter
        psi_values = list(self.report['detailed_results']['psi'].values())
        scatter = ax2.scatter(cpi_values, psi_values, alpha=0.6, s=50)
        ax2.axhline(y=1.0, color='red', linestyle='--', alpha=0.7, label='Stability Baseline')
        ax2.axvline(x=1.0, color='red', linestyle='--', alpha=0.7, label='Performance Baseline')
        ax2.set_xlabel('Comprehensive Performance Index (CPI)')
        ax2.set_ylabel('Performance Stability Index (PSI)')
        ax2.set_title('Performance vs Stability Trade-off')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # AER Distribution
        aer_values = list(self.report['detailed_results']['aer'].values())
        ax3.hist(aer_values, bins=20, alpha=0.7, color='lightgreen', edgecolor='black')
        ax3.axvline(x=1.0, color='red', linestyle='--', linewidth=2, label='Architecture Baseline')
        ax3.axvline(x=np.mean(aer_values), color='orange', linestyle='-', linewidth=2, label=f'Mean AER: {np.mean(aer_values):.3f}')
        ax3.set_xlabel('Architectural Efficiency Ratio (AER)')
        ax3.set_ylabel('Frequency')
        ax3.set_title('Architectural Efficiency Distribution')
        ax3.legend()
        ax3.grid(True, alpha=0.3)

        # Performance Summary Bar Chart
        metrics = ['CPI', 'PSI', 'AER']
        means = [
            self.report['comprehensive_performance_index']['mean'],
            self.report['performance_stability_index']['mean'],
            self.report['architectural_efficiency_ratio']['mean']
        ]
        colors = ['skyblue', 'lightcoral', 'lightgreen']

        bars = ax4.bar(metrics, means, color=colors, alpha=0.7, edgecolor='black')
        ax4.axhline(y=1.0, color='red', linestyle='--', linewidth=2, label='Baseline')
        ax4.set_ylabel('Metric Value')
        ax4.set_title('Performance Metrics Summary')
        ax4.legend()
        ax4.grid(True, alpha=0.3)

        # Add value labels on bars
        for bar, mean in zip(bars, means):
            height = bar.get_height()
            ax4.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{mean:.3f}', ha='center', va='bottom', fontweight='bold')

        plt.tight_layout()
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        plt.close()

    def plot_metric_comparison(self, filename: str):
        """Create detailed metric comparison plot"""
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        axes = axes.flatten()

        # Performance dimensions from PDS
        dimensions = ['throughput', 'cpu_efficiency', 'memory_efficiency',
                     'cache_efficiency', 'branch_prediction_accuracy']

        pds_results = self.report['detailed_results']['pds']

        for i, dim in enumerate(dimensions):
            values = [result[dim] for result in pds_results.values()]

            axes[i].hist(values, bins=15, alpha=0.7, edgecolor='black')
            axes[i].axvline(x=1.0, color='red', linestyle='--', linewidth=2, label='Ground Truth')
            axes[i].axvline(x=np.mean(values), color='orange', linestyle='-', linewidth=2,
                           label=f'Mean: {np.mean(values):.3f}')
            axes[i].set_xlabel(f'{dim.replace("_", " ").title()} Ratio')
            axes[i].set_ylabel('Frequency')
            axes[i].set_title(f'{dim.replace("_", " ").title()} Distribution')
            axes[i].legend()
            axes[i].grid(True, alpha=0.3)

        # Overall dominance score
        dominance_scores = [result['overall_pds'] for result in pds_results.values()]
        axes[5].hist(dominance_scores, bins=6, alpha=0.7, color='purple', edgecolor='black')
        axes[5].set_xlabel('Performance Dominance Score')
        axes[5].set_ylabel('Frequency')
        axes[5].set_title('Overall Performance Dominance')
        axes[5].axvline(x=np.mean(dominance_scores), color='orange', linestyle='-', linewidth=2,
                       label=f'Mean: {np.mean(dominance_scores):.3f}')
        axes[5].legend()
        axes[5].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        plt.close()

    def plot_dominance_analysis(self, filename: str):
        """Create performance dominance analysis plot"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

        pds_results = self.report['detailed_results']['pds']
        dimensions = ['throughput', 'cpu_efficiency', 'memory_efficiency',
                     'cache_efficiency', 'branch_prediction_accuracy']

        # Dominance frequency per dimension
        dominance_counts = {}
        for dim in dimensions:
            dominance_counts[dim] = sum(1 for result in pds_results.values() if result[dim] > 1.0)

        dim_labels = [dim.replace('_', ' ').title() for dim in dimensions]
        counts = list(dominance_counts.values())
        total_benchmarks = len(pds_results)

        bars = ax1.bar(dim_labels, counts, alpha=0.7, color='steelblue', edgecolor='black')
        ax1.set_ylabel('Number of Benchmarks')
        ax1.set_title('Performance Dominance by Dimension')
        ax1.set_ylim(0, total_benchmarks)

        # Add percentage labels
        for bar, count in zip(bars, counts):
            height = bar.get_height()
            percentage = (count / total_benchmarks) * 100
            ax1.text(bar.get_x() + bar.get_width()/2., height + 1,
                    f'{percentage:.1f}%', ha='center', va='bottom', fontweight='bold')

        plt.setp(ax1.get_xticklabels(), rotation=45, ha='right')
        ax1.grid(True, alpha=0.3)

        # Overall dominance distribution
        dominance_scores = [result['overall_pds'] for result in pds_results.values()]
        dominance_categories = ['0%', '20%', '40%', '60%', '80%', '100%']
        dominance_counts_cat = [0] * 6

        for score in dominance_scores:
            category = min(int(score * 5), 5)
            dominance_counts_cat[category] += 1

        colors = plt.cm.RdYlGn(np.linspace(0.2, 0.8, 6))
        wedges, texts, autotexts = ax2.pie(dominance_counts_cat, labels=dominance_categories,
                                          autopct='%1.1f%%', colors=colors, startangle=90)
        ax2.set_title('Overall Performance Dominance Distribution')

        plt.tight_layout()
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        plt.close()

    def plot_efficiency_at_k(self, filename: str):
        """Plot Efficiency@k analysis"""
        fig, ax = plt.subplots(1, 1, figsize=(10, 6))

        efficiency_k = self.report['efficiency_at_k']
        k_values = list(efficiency_k.keys())
        efficiency_values = list(efficiency_k.values())

        ax.plot(k_values, efficiency_values, marker='o', linewidth=3, markersize=8,
                color='darkblue', label='Efficiency@k')
        ax.fill_between(k_values, efficiency_values, alpha=0.3, color='lightblue')

        ax.set_xlabel('k (Number of Generated Solutions)')
        ax.set_ylabel('Efficiency@k (Probability of Better Performance)')
        ax.set_title('Efficiency@k: Probability of Generating Better Solutions')
        ax.grid(True, alpha=0.3)
        ax.legend()

        # Add value labels
        for k, eff in zip(k_values, efficiency_values):
            ax.text(k, eff + 0.01, f'{eff:.3f}', ha='center', va='bottom', fontweight='bold')

        ax.set_ylim(0, 1.0)
        ax.set_xlim(0.5, max(k_values) + 0.5)

        plt.tight_layout()
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        plt.close()

    def plot_stability_analysis(self, filename: str):
        """Create performance stability analysis"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

        cpi_values = list(self.report['detailed_results']['cpi'].values())
        psi_values = list(self.report['detailed_results']['psi'].values())

        # Stability vs Performance quadrant analysis
        stable_fast = sum(1 for cpi, psi in zip(cpi_values, psi_values) if cpi > 1.0 and psi < 1.0)
        stable_slow = sum(1 for cpi, psi in zip(cpi_values, psi_values) if cpi <= 1.0 and psi < 1.0)
        unstable_fast = sum(1 for cpi, psi in zip(cpi_values, psi_values) if cpi > 1.0 and psi >= 1.0)
        unstable_slow = sum(1 for cpi, psi in zip(cpi_values, psi_values) if cpi <= 1.0 and psi >= 1.0)

        # Quadrant scatter plot
        colors = []
        for cpi, psi in zip(cpi_values, psi_values):
            if cpi > 1.0 and psi < 1.0:
                colors.append('green')  # Stable & Fast
            elif cpi <= 1.0 and psi < 1.0:
                colors.append('blue')   # Stable & Slow
            elif cpi > 1.0 and psi >= 1.0:
                colors.append('orange') # Unstable & Fast
            else:
                colors.append('red')    # Unstable & Slow

        scatter = ax1.scatter(cpi_values, psi_values, c=colors, alpha=0.6, s=50)
        ax1.axhline(y=1.0, color='black', linestyle='--', alpha=0.7)
        ax1.axvline(x=1.0, color='black', linestyle='--', alpha=0.7)
        ax1.set_xlabel('Comprehensive Performance Index (CPI)')
        ax1.set_ylabel('Performance Stability Index (PSI)')
        ax1.set_title('Performance-Stability Quadrant Analysis')

        # Add quadrant labels
        ax1.text(1.5, 0.5, 'Stable &\nFast', ha='center', va='center', fontsize=10,
                bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgreen", alpha=0.7))
        ax1.text(0.5, 0.5, 'Stable &\nSlow', ha='center', va='center', fontsize=10,
                bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.7))
        ax1.text(1.5, 1.5, 'Unstable &\nFast', ha='center', va='center', fontsize=10,
                bbox=dict(boxstyle="round,pad=0.3", facecolor="lightyellow", alpha=0.7))
        ax1.text(0.5, 1.5, 'Unstable &\nSlow', ha='center', va='center', fontsize=10,
                bbox=dict(boxstyle="round,pad=0.3", facecolor="lightcoral", alpha=0.7))

        ax1.grid(True, alpha=0.3)

        # Quadrant distribution pie chart
        quadrant_counts = [stable_fast, stable_slow, unstable_fast, unstable_slow]
        quadrant_labels = ['Stable & Fast', 'Stable & Slow', 'Unstable & Fast', 'Unstable & Slow']
        quadrant_colors = ['green', 'blue', 'orange', 'red']

        wedges, texts, autotexts = ax2.pie(quadrant_counts, labels=quadrant_labels,
                                          autopct='%1.1f%%', colors=quadrant_colors,
                                          startangle=90)
        ax2.set_title('Performance-Stability Distribution')

        plt.tight_layout()
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        plt.close()


if __name__ == "__main__":
    # Create analyzer and visualizer
    analyzer = AdvancedPerformanceAnalyzer(
        ground_truth_dir="../../JMH-perfnorm/benchmark_results/20250509_155655_ground_truth",
        generated_code_dir="../../models/copilot/benchmark_results/20250707_175350_copilot"
    )

    analyzer.load_benchmark_data()

    visualizer = PerformanceVisualizer(analyzer)
    visualizer.generate_all_plots("publication_plots")

    print("Publication-quality plots generated successfully!")
