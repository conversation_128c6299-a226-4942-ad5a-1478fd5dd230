#!/usr/bin/env python3
"""
实际计算示例：展示如何从JMH数据计算新性能指标
"""

import json
import numpy as np
from typing import Dict, List, Tuple
from dataclasses import dataclass

@dataclass
class PerformanceProfile:
    """性能特征配置文件"""
    throughput: float
    cpu_efficiency: float  # IPC
    memory_efficiency: float
    cache_efficiency: float
    branch_prediction_accuracy: float

class MetricsCalculationExample:
    """性能指标计算示例类"""
    
    def __init__(self):
        self.example_gt_data = None
        self.example_gen_data = None
        self.load_example_data()
    
    def load_example_data(self):
        """加载示例JMH数据"""
        # 这里使用您实际的JMH数据结构
        self.example_gt_data = {
            "primaryMetric": {
                "score": 0.3859887889639006,  # ops/ms
                "rawData": [
                    [0.51761997531938, 0.4660752251900585, 0.42973995962329237],
                    [0.40304472194011476, 0.3807289202473261, 0.36149713037935755],
                    # ... 更多数据
                ]
            },
            "secondaryMetrics": {
                "cpu_atom/instructions/": {"score": 3.670512258412002E7},
                "cpu_atom/cycles/": {"score": 1.4821863225257989E7},
                "cpu_atom/L1-dcache-loads/": {"score": 1.1853666070564952E7},
                "cpu_atom/L1-dcache-load-misses/": {"score": 16689.65689866224},
                "cpu_atom/branches/": {"score": 7023519.541969268},
                "cpu_atom/branch-misses/": {"score": 24440.642523711496},
                "cpu_atom/dTLB-loads/": {"score": 1.159942906199726E7},
                "cpu_atom/dTLB-load-misses/": {"score": 957.8222294126402},
                "cpu_atom/iTLB-load-misses/": {"score": 4086.213883007781},
                "gc.alloc.rate": {"score": 1553.7998737133187}
            }
        }
        
        self.example_gen_data = {
            "primaryMetric": {
                "score": 0.3329978230256796,  # ops/ms
                "rawData": [
                    [0.425469140451582, 0.5142544806511098, 0.38885724150410017],
                    [0.3284193909713151, 0.28730157762194364, 0.28989869229289],
                    # ... 更多数据
                ]
            },
            "secondaryMetrics": {
                "cpu_atom/instructions/": {"score": 3.670512258412002E7},
                "cpu_atom/cycles/": {"score": 1.4821863225257989E7},
                "cpu_atom/L1-dcache-loads/": {"score": 1.1853666070564952E7},
                "cpu_atom/L1-dcache-load-misses/": {"score": 16689.65689866224},
                "cpu_atom/branches/": {"score": 7023519.541969268},
                "cpu_atom/branch-misses/": {"score": 24440.642523711496},
                "cpu_atom/dTLB-loads/": {"score": 1.159942906199726E7},
                "cpu_atom/dTLB-load-misses/": {"score": 957.8222294126402},
                "cpu_atom/iTLB-load-misses/": {"score": 4086.213883007781},
                "gc.alloc.rate": {"score": 1597.9}
            }
        }
    
    def get_metric_score(self, secondary_metrics: Dict, metric_pattern: str) -> float:
        """从次要指标中提取分数"""
        for key, value in secondary_metrics.items():
            if metric_pattern in key:
                score = value.get('score', 0.0)
                try:
                    return float(score) if score != "NaN" else 0.0
                except (ValueError, TypeError):
                    return 0.0
        return 0.0
    
    def extract_performance_profile(self, benchmark_data: Dict) -> PerformanceProfile:
        """提取性能特征配置文件"""
        primary = benchmark_data.get('primaryMetric', {})
        secondary = benchmark_data.get('secondaryMetrics', {})
        
        print("=== 性能特征提取过程 ===")
        
        # 1. 吞吐量
        throughput = primary.get('score', 0.0)
        print(f"1. 吞吐量: {throughput:.6f} ops/ms")
        
        # 2. CPU效率 (IPC)
        instructions = self.get_metric_score(secondary, 'instructions')
        cycles = self.get_metric_score(secondary, 'cycles')
        cpu_efficiency = instructions / cycles if cycles > 0 else 0.0
        print(f"2. CPU效率 (IPC):")
        print(f"   - 指令数: {instructions:.0f}")
        print(f"   - 周期数: {cycles:.0f}")
        print(f"   - IPC: {cpu_efficiency:.6f}")
        
        # 3. 内存效率
        gc_alloc_rate = self.get_metric_score(secondary, 'gc.alloc.rate')
        memory_efficiency = 1.0 / (1.0 + gc_alloc_rate / 1000.0) if gc_alloc_rate > 0 else 1.0
        print(f"3. 内存效率:")
        print(f"   - GC分配率: {gc_alloc_rate:.2f} MB/sec")
        print(f"   - 内存效率: {memory_efficiency:.6f}")
        
        # 4. 缓存效率
        l1_loads = self.get_metric_score(secondary, 'L1-dcache-loads')
        l1_misses = self.get_metric_score(secondary, 'L1-dcache-load-misses')
        cache_efficiency = 1.0 - (l1_misses / l1_loads) if l1_loads > 0 else 1.0
        print(f"4. 缓存效率:")
        print(f"   - L1缓存访问: {l1_loads:.0f}")
        print(f"   - L1缓存缺失: {l1_misses:.0f}")
        print(f"   - 缓存命中率: {cache_efficiency:.6f}")
        
        # 5. 分支预测准确率
        branches = self.get_metric_score(secondary, 'branches')
        branch_misses = self.get_metric_score(secondary, 'branch-misses')
        branch_prediction_accuracy = 1.0 - (branch_misses / branches) if branches > 0 else 1.0
        print(f"5. 分支预测准确率:")
        print(f"   - 分支总数: {branches:.0f}")
        print(f"   - 分支预测错误: {branch_misses:.0f}")
        print(f"   - 预测准确率: {branch_prediction_accuracy:.6f}")
        
        return PerformanceProfile(
            throughput=throughput,
            cpu_efficiency=cpu_efficiency,
            memory_efficiency=memory_efficiency,
            cache_efficiency=cache_efficiency,
            branch_prediction_accuracy=branch_prediction_accuracy
        )
    
    def calculate_cpi_step_by_step(self) -> float:
        """逐步计算综合性能指数"""
        print("\n" + "="*60)
        print("综合性能指数 (CPI) 计算过程")
        print("="*60)
        
        # 提取性能特征
        print("\n【基准实现性能特征】")
        gt_profile = self.extract_performance_profile(self.example_gt_data)
        
        print("\n【生成代码性能特征】")
        gen_profile = self.extract_performance_profile(self.example_gen_data)
        
        # 计算各维度比率
        print("\n【维度比率计算】")
        weights = {
            'throughput': 0.40,
            'cpu_efficiency': 0.25,
            'memory_efficiency': 0.20,
            'cache_efficiency': 0.10,
            'branch_prediction': 0.05
        }
        
        ratios = {}
        ratios['throughput'] = gen_profile.throughput / gt_profile.throughput if gt_profile.throughput > 0 else 0
        ratios['cpu_efficiency'] = gen_profile.cpu_efficiency / gt_profile.cpu_efficiency if gt_profile.cpu_efficiency > 0 else 0
        ratios['memory_efficiency'] = gen_profile.memory_efficiency / gt_profile.memory_efficiency if gt_profile.memory_efficiency > 0 else 0
        ratios['cache_efficiency'] = gen_profile.cache_efficiency / gt_profile.cache_efficiency if gt_profile.cache_efficiency > 0 else 0
        ratios['branch_prediction'] = gen_profile.branch_prediction_accuracy / gt_profile.branch_prediction_accuracy if gt_profile.branch_prediction_accuracy > 0 else 0
        
        for metric, ratio in ratios.items():
            print(f"{metric:20s}: {ratio:.6f} (权重: {weights[metric]:.2f})")
        
        # 计算加权CPI
        print("\n【CPI计算】")
        cpi = sum(weights[metric] * ratios[metric] for metric in weights.keys())
        
        print("CPI = ", end="")
        terms = []
        for metric in weights.keys():
            terms.append(f"{weights[metric]:.2f} × {ratios[metric]:.6f}")
        print(" + ".join(terms))
        print(f"    = {cpi:.6f}")
        
        # 解释结果
        print(f"\n【结果解释】")
        if cpi > 1.0:
            print(f"✓ 生成代码性能优于基准 ({cpi:.3f}x)")
        elif cpi == 1.0:
            print(f"= 生成代码性能与基准相当")
        else:
            print(f"✗ 生成代码性能低于基准 ({cpi:.3f}x)")
        
        return cpi
    
    def calculate_psi_step_by_step(self) -> float:
        """逐步计算性能稳定性指数"""
        print("\n" + "="*60)
        print("性能稳定性指数 (PSI) 计算过程")
        print("="*60)
        
        # 提取原始数据
        gt_raw = self.extract_raw_throughput(self.example_gt_data)
        gen_raw = self.extract_raw_throughput(self.example_gen_data)
        
        print(f"基准原始数据: {gt_raw}")
        print(f"生成代码原始数据: {gen_raw}")
        
        # 计算统计量
        gt_mean = np.mean(gt_raw)
        gt_std = np.std(gt_raw)
        gt_cv = gt_std / gt_mean if gt_mean > 0 else float('inf')
        
        gen_mean = np.mean(gen_raw)
        gen_std = np.std(gen_raw)
        gen_cv = gen_std / gen_mean if gen_mean > 0 else float('inf')
        
        print(f"\n【统计量计算】")
        print(f"基准实现:")
        print(f"  均值: {gt_mean:.6f}")
        print(f"  标准差: {gt_std:.6f}")
        print(f"  变异系数: {gt_cv:.6f}")
        
        print(f"生成代码:")
        print(f"  均值: {gen_mean:.6f}")
        print(f"  标准差: {gen_std:.6f}")
        print(f"  变异系数: {gen_cv:.6f}")
        
        # 计算PSI
        psi = gen_cv / gt_cv if gt_cv > 0 else float('inf')
        
        print(f"\n【PSI计算】")
        print(f"PSI = {gen_cv:.6f} / {gt_cv:.6f} = {psi:.6f}")
        
        # 解释结果
        print(f"\n【结果解释】")
        if psi < 1.0:
            print(f"✓ 生成代码更稳定 (PSI = {psi:.3f})")
        elif psi == 1.0:
            print(f"= 稳定性相当")
        else:
            print(f"✗ 生成代码不够稳定 (PSI = {psi:.3f})")
        
        return psi
    
    def extract_raw_throughput(self, benchmark_data: Dict) -> List[float]:
        """提取原始吞吐量数据"""
        primary = benchmark_data.get('primaryMetric', {})
        raw_data = primary.get('rawData', [])
        
        # 展平数据
        flattened = []
        for fork_data in raw_data:
            flattened.extend(fork_data)
        
        return flattened
    
    def calculate_aer_step_by_step(self) -> float:
        """逐步计算架构效率比"""
        print("\n" + "="*60)
        print("架构效率比 (AER) 计算过程")
        print("="*60)
        
        gt_secondary = self.example_gt_data.get('secondaryMetrics', {})
        gen_secondary = self.example_gen_data.get('secondaryMetrics', {})
        
        # 1. 缓存效率
        print("\n【1. 缓存效率计算】")
        gt_cache_eff = self.calculate_cache_efficiency(gt_secondary)
        gen_cache_eff = self.calculate_cache_efficiency(gen_secondary)
        cache_ratio = gen_cache_eff / gt_cache_eff if gt_cache_eff > 0 else 0
        
        print(f"基准缓存效率: {gt_cache_eff:.6f}")
        print(f"生成代码缓存效率: {gen_cache_eff:.6f}")
        print(f"缓存效率比: {cache_ratio:.6f}")
        
        # 2. TLB效率
        print("\n【2. TLB效率计算】")
        gt_tlb_eff = self.calculate_tlb_efficiency(gt_secondary)
        gen_tlb_eff = self.calculate_tlb_efficiency(gen_secondary)
        tlb_ratio = gen_tlb_eff / gt_tlb_eff if gt_tlb_eff > 0 else 0
        
        print(f"基准TLB效率: {gt_tlb_eff:.6f}")
        print(f"生成代码TLB效率: {gen_tlb_eff:.6f}")
        print(f"TLB效率比: {tlb_ratio:.6f}")
        
        # 3. IPC
        print("\n【3. IPC计算】")
        gt_ipc = self.calculate_ipc(gt_secondary)
        gen_ipc = self.calculate_ipc(gen_secondary)
        ipc_ratio = gen_ipc / gt_ipc if gt_ipc > 0 else 0
        
        print(f"基准IPC: {gt_ipc:.6f}")
        print(f"生成代码IPC: {gen_ipc:.6f}")
        print(f"IPC比: {ipc_ratio:.6f}")
        
        # 计算AER
        print("\n【AER计算】")
        aer = (cache_ratio * 0.4 + tlb_ratio * 0.3 + ipc_ratio * 0.3)
        print(f"AER = 0.4 × {cache_ratio:.6f} + 0.3 × {tlb_ratio:.6f} + 0.3 × {ipc_ratio:.6f}")
        print(f"    = {aer:.6f}")
        
        return aer
    
    def calculate_cache_efficiency(self, secondary_metrics: Dict) -> float:
        """计算缓存效率"""
        l1_loads = self.get_metric_score(secondary_metrics, 'L1-dcache-loads')
        l1_misses = self.get_metric_score(secondary_metrics, 'L1-dcache-load-misses')
        
        return 1.0 - (l1_misses / l1_loads) if l1_loads > 0 else 0.0
    
    def calculate_tlb_efficiency(self, secondary_metrics: Dict) -> float:
        """计算TLB效率"""
        loads = self.get_metric_score(secondary_metrics, 'dTLB-loads')
        misses = (self.get_metric_score(secondary_metrics, 'dTLB-load-misses') + 
                 self.get_metric_score(secondary_metrics, 'iTLB-load-misses'))
        
        return 1.0 - (misses / loads) if loads > 0 else 0.0
    
    def calculate_ipc(self, secondary_metrics: Dict) -> float:
        """计算IPC"""
        instructions = self.get_metric_score(secondary_metrics, 'instructions')
        cycles = self.get_metric_score(secondary_metrics, 'cycles')
        
        return instructions / cycles if cycles > 0 else 0.0
    
    def run_complete_example(self):
        """运行完整示例"""
        print("AI生成代码性能指标计算完整示例")
        print("="*80)
        
        # 计算所有指标
        cpi = self.calculate_cpi_step_by_step()
        psi = self.calculate_psi_step_by_step()
        aer = self.calculate_aer_step_by_step()
        
        # 总结
        print("\n" + "="*60)
        print("指标计算总结")
        print("="*60)
        print(f"综合性能指数 (CPI): {cpi:.6f}")
        print(f"性能稳定性指数 (PSI): {psi:.6f}")
        print(f"架构效率比 (AER): {aer:.6f}")
        
        print(f"\n整体评估:")
        if cpi > 1.0 and psi < 1.0 and aer > 1.0:
            print("✓ 生成代码在性能、稳定性和架构效率方面都表现优秀")
        elif cpi > 1.0:
            print("✓ 生成代码性能优秀，但需要关注稳定性")
        else:
            print("⚠ 生成代码需要进一步优化")


if __name__ == "__main__":
    example = MetricsCalculationExample()
    example.run_complete_example()
