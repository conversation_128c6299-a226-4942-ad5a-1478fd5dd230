{"summary_statistics": {"total_benchmarks": 150, "ground_truth_benchmarks": 185, "generated_benchmarks": 150, "success_rate": 0.8108108108108109}, "comprehensive_performance_index": {"mean": 1.983172319419072, "median": 2.0278108466083675, "std": 0.34432898249731686, "better_than_gt_ratio": 0.96}, "performance_stability_index": {"mean": 1.2909900342598197, "median": 1.285036397302096, "more_stable_ratio": 0.02666666666666667}, "architectural_efficiency_ratio": {"mean": 2.1706589755270094, "median": 2.23290729735486, "better_arch_utilization_ratio": 0.96}, "performance_dominance_score": {"mean_dominance": 0.7906666666666664, "full_dominance_ratio": 0.0}, "efficiency_at_k": {"1": 0.9533333333333334, "3": 0.96, "5": 0.9733333333333334}, "detailed_results": {"cpi": {"Solution78_nthMagicalNumber_0_1_Test": 1.7834791946924007, "Solution102_minCostToMoveChips_0_0_Test": 1.4412264842977722, "Solution93_numMovesStones_0_1_Test": 1.7355472532416902, "Solution1_romanToInt_0_0_Test": 1.90981473495507, "Solution126_countOdds_0_9_Test": 2.034165280110746, "Solution91_divisorGame_0_0_Test": 2.275039415828765, "Solution20_minTotalDistance_0_0_Test": 1.9172199938570398, "Solution30_superPow_0_1_Test": 2.2061457156929194, "Solution122_winnerSquareGame_0_3_Test": 2.6097773417812844, "Solution75_primePalindrome_0_0_Test": 2.1109157811343944, "Solution76_reorderedPowerOf2_0_0_Test": 3.1897232359044114, "Solution60_countCornerRectangles_0_1_Test": 2.5853944257772983, "Solution24_isPowerOfFour_0_0_Test": 1.7399435833982007, "Solution73_numMagicSquaresInside_0_0_Test": 1.818678631070963, "Solution125_numWaterBottles_0_3_Test": 2.225340560308843, "Solution15_addDigits_0_0_Test": 2.0427806261854053, "Solution79_projectionArea_0_0_Test": 2.273651866130246, "Solution26_countNumbersWithUniqueDigits_0_0_Test": 1.****************, "Solution166_countDigits_0_1_Test": 1.****************, "Solution35_poorPigs_0_1_Test": 2.***************, "Solution23_isSelfCrossing_0_0_Test": 2.****************, "Solution18_numSquares_0_0_Test": 2.**************, "Solution136_totalMoney_0_0_Test": 2.****************, "Solution183_sumOfTheDigitsOfHarshadNumber_0_0_Test": 1.****************, "Solution6_mySqrt_0_0_Test": 2.***************, "Solution94_isBoomerang_0_0_Test": 1.****************, "Solution179_accountBalanceAfterPurchase_0_2_Test": 1.****************, "Solution104_missingNumber_0_0_Test": 1.****************, "Solution72_new21Game_0_2_Test": 1.***************, "Solution16_isUgly_0_0_Test": 1.***************, "Solution28_isPerfectSquare_0_0_Test": 1.***************, "Solution143_reinitializePermutation_0_0_Test": 2.****************, "Solution22_isPowerOfThree_0_0_Test": 1.****************, "Solution86_minAreaRect_0_0_Test": 2.****************, "Solution172_sumOfMultiples_1_0_Test": 1.****************, "Solution107_numberOfSubarrays_0_3_Test": 2.****************, "Solution31_getMoneyAmount_0_0_Test": 2.***************, "Solution11_countPrimes_0_0_Test": 2.****************, "Solution8_maxPoints_0_0_Test": 2.***************, "Solution34_numberOfBoomerangs_0_0_Test": 2.***************, "Solution128_maxCoins_0_0_Test": 2.****************, "Solution39_constructRectangle_0_0_Test": 2.****************, "Solution61_reachNumber_0_0_Test": 1.****************, "Solution0_intToRoman_0_0_Test": 2.2471413314908832, "Solution147_findTheWinner_0_0_Test": 1.7542211809669583, "Solution109_oddCells_0_0_Test": 2.1772082708763176, "Solution48_smallestFactorization_0_1_Test": 1.7327594680014513, "Solution144_countNicePairs_0_0_Test": 1.9466489667421778, "Solution38_PredictTheWinner_0_0_Test": 1.838520490912235, "Solution82_superpalindromesInRange_0_0_Test": 1.6754795522394628, "Solution67_similarRGB_0_0_Test": 2.0174188645281275, "Solution21_bulbSwitch_0_0_Test": 2.299640565667033, "Solution2_divide_0_0_Test": 1.9955763618247624, "Solution88_kClosest_0_0_Test": 2.472947717444136, "Solution146_arraySign_0_0_Test": 1.7855236230994083, "Solution44_minDistance_1_0_Test": 1.7188523651084757, "Solution110_numOfBurgers_0_0_Test": 1.662725297049576, "Solution40_checkPerfectNumber_0_0_Test": 1.6711422339983872, "Solution117_findMinFibonacciNumbers_0_0_Test": 2.1545425276746117, "Solution84_numMusicPlaylists_0_4_Test": 1.5994476488184584, "Solution127_numOfSubarrays_0_4_Test": 2.0722183912460386, "Solution160_smallestEvenMultiple_0_0_Test": 1.6138873439611272, "Solution56_maximumSwap_0_0_Test": 1.5344615787967446, "Solution7_climbStairs_0_0_Test": 1.7973035804706776, "Solution119_simplifiedFractions_0_0_Test": 2.2590277964372056, "Solution49_maximumProduct_0_0_Test": 2.43622291259698, "Solution27_canMeasureWater_0_0_Test": 2.0541123233258096, "Solution99_isArmstrong_0_0_Test": 2.0285774557758596, "Solution106_checkStraightLine_0_0_Test": 2.1737894229549894, "Solution153_winnerOfGame_0_0_Test": 2.237615374177794, "Solution65_rotatedDigits_0_1_Test": 2.1195392893783227, "Solution90_smallestRepunitDivByK_0_0_Test": 1.9484955722487438, "Solution32_fizzBuzz_0_0_Test": 2.0062618115024553, "Solution63_numRabbits_0_0_Test": 0.7656783478122957, "Solution71_isRectangleOverlap_0_0_Test": 1.9435539479032742, "Solution77_stoneGame_0_0_Test": 2.3973113161754203, "Solution114_sumFourDivisors_0_1_Test": 2.0776737069505766, "Solution105_probabilityOfHeads_0_1_Test": 2.0144992356133335, "Solution41_fib_0_0_Test": 1.8919230378754461, "Solution100_tribonacci_0_0_Test": 1.948420818639118, "Solution70_consecutiveNumbersSum_0_0_Test": 0.7561432311850406, "Solution66_preimageSizeFZF_0_1_Test": 0.745141065454114, "Solution98_distributeCandies_0_0_Test": 2.0429079697907264, "Solution50_judgeSquareSum_0_0_Test": 2.1582568079423696, "Solution74_mirrorReflection_0_0_Test": 2.196452121795246, "Solution115_countLargestGroup_0_9_Test": 2.240760248393006, "Solution17_nthUglyNumber_0_0_Test": 2.324916649726754, "Solution46_validSquare_0_0_Test": 2.177961630434445, "Solution69_largestTriangleArea_0_0_Test": 2.085080804492536, "Solution19_canWinNim_0_1_Test": 2.102519950900423, "Solution157_countEven_0_0_Test": 2.1174391046348977, "Solution159_waysToBuyPensPencils_0_8_Test": 1.571633220013918, "Solution53_newInteger_0_2_Test": 2.046708821281211, "Solution134_numberOfMatches_0_0_Test": 0.7512890115831531, "Solution36_canIWin_0_0_Test": 2.1235914827783557, "Solution57_flipLights_0_1_Test": 2.0565938299893483, "Solution47_maxCount_0_0_Test": 2.1134446217797223, "Solution13_isPowerOfTwo_0_0_Test": 1.87042129206241, "Solution175_count_0_0_Test": 2.0975479686830103, "Solution37_largestPalindrome_0_0_Test": 2.2220084117778787, "Solution113_angleClock_0_0_Test": 1.9180742633086738, "Solution83_hasGroupsSizeX_0_0_Test": 0.7556595421250599, "Solution52_solveEquation_0_0_Test": 1.798543601333664, "Solution5_uniquePaths_0_0_Test": 2.0270442374408755, "Solution145_countDifferentSubsequenceGCDs_0_0_Test": 2.1775912253101093, "Solution133_stoneGameVI_0_0_Test": 2.147042064177582, "Solution167_alternateDigitSum_0_0_Test": 2.1956777511245154, "Solution135_stoneGameVII_0_0_Test": 1.770090626383061, "Solution89_largestPerimeter_0_0_Test": 0.7622867670650355, "Solution139_countBalls_0_0_Test": 1.831076591297945, "Solution158_sum_0_0_Test": 1.9539690942065298, "Solution108_isGoodArray_0_1_Test": 1.8728835962212078, "Solution12_computeArea_0_0_Test": 2.18079177598115, "Solution101_numPrimeArrangements_0_0_Test": 2.0061119501236795, "Solution152_stoneGameIX_0_2_Test": 2.073524779302499, "Solution25_integerBreak_0_0_Test": 1.8537675227322272, "Solution9_trailingZeroes_0_0_Test": 1.6368335240250402, "Solution96_addNegabinary_0_8_Test": 2.216844076184783, "Solution123_numIdenticalPairs_0_0_Test": 2.1203585175617006, "Solution131_sumOddLengthSubarrays_0_0_Test": 2.1131385725627823, "Solution129_stoneGameV_0_0_Test": 2.155816013335035, "Solution121_kthFactor_0_1_Test": 2.0980217327360617, "Solution14_countDigitOne_0_0_Test": 1.8598154980498, "Solution58_selfDividingNumbers_0_0_Test": 2.2890583541243497, "Solution33_arrangeCoins_0_0_Test": 2.0486760232389023, "Solution124_getMinDistSum_0_0_Test": 2.0221615514137814, "Solution111_subtractProductAndSum_0_0_Test": 1.9321541071599584, "Solution116_checkOverlap_0_0_Test": 1.9506760021716454, "Solution148_countPoints_0_0_Test": 2.171487365235318, "Solution161_commonFactors_0_2_Test": 2.0589735977802337, "Solution141_checkPowersOfThree_0_0_Test": 1.9048134188833614, "Solution169_splitNum_0_0_Test": 2.31231742876269, "Solution112_numberOfSteps_0_0_Test": 2.2155357860175657, "Solution42_complexNumberMultiply_0_0_Test": 1.6666892890420637, "Solution103_nthPersonGetsNthSeat_0_0_Test": 2.1677538885781247, "Solution10_isHappy_1_0_Test": 2.1153218636097044, "Solution55_findKthNumber_0_2_Test": 1.8754296671060895, "Solution162_averageValue_0_0_Test": 1.8707616265190337, "Solution87_powerfulIntegers_0_0_Test": 2.2331242589911104, "Solution80_superEggDrop_0_0_Test": 1.658746504603748, "Solution4_getPermutation_0_0_Test": 2.157869110934413, "Solution92_allCellsDistOrder_0_0_Test": 1.839841551545052, "Solution81_surfaceArea_0_0_Test": 1.8783673616577525, "Solution184_maximumPrimeDifference_0_1_Test": 2.0007610552377533, "Solution29_largestDivisibleSubset_0_3_Test": 2.1804784604480125, "Solution151_missingRolls_0_0_Test": 2.0860602103595784, "Solution68_soupServings_0_0_Test": 1.9882003622238122, "Solution62_reachingPoints_0_0_Test": 2.1396711381694997, "Solution132_visiblePoints_0_1_Test": 1.94412861296986, "Solution59_monotoneIncreasingDigits_0_0_Test": 1.9713849374694692}, "psi": {"Solution78_nthMagicalNumber_0_1_Test": 1.4066484009637361, "Solution102_minCostToMoveChips_0_0_Test": 1.281667147040702, "Solution93_numMovesStones_0_1_Test": 1.3149151568831503, "Solution1_romanToInt_0_0_Test": 1.3979454453346443, "Solution126_countOdds_0_9_Test": 1.3345679560787058, "Solution91_divisorGame_0_0_Test": 1.5255651480733396, "Solution20_minTotalDistance_0_0_Test": 1.3922656981626027, "Solution30_superPow_0_1_Test": 1.213444656980054, "Solution122_winnerSquareGame_0_3_Test": 1.6179819077834332, "Solution75_primePalindrome_0_0_Test": 1.2898725963978535, "Solution76_reorderedPowerOf2_0_0_Test": 1.157577743405033, "Solution60_countCornerRectangles_0_1_Test": 0.9625878718806056, "Solution24_isPowerOfFour_0_0_Test": 1.2537681002439396, "Solution73_numMagicSquaresInside_0_0_Test": 1.0831977844350276, "Solution125_numWaterBottles_0_3_Test": 1.5592867006420932, "Solution15_addDigits_0_0_Test": 1.0503465100269216, "Solution79_projectionArea_0_0_Test": 1.3072286321556525, "Solution26_countNumbersWithUniqueDigits_0_0_Test": 1.****************, "Solution166_countDigits_0_1_Test": 1.***************, "Solution35_poorPigs_0_1_Test": 1.****************, "Solution23_isSelfCrossing_0_0_Test": 1.****************, "Solution18_numSquares_0_0_Test": 1.****************, "Solution136_totalMoney_0_0_Test": 1.****************, "Solution183_sumOfTheDigitsOfHarshadNumber_0_0_Test": 1.****************, "Solution6_mySqrt_0_0_Test": 1.***************, "Solution94_isBoomerang_0_0_Test": 1.****************, "Solution179_accountBalanceAfterPurchase_0_2_Test": 1.**************, "Solution104_missingNumber_0_0_Test": 1.****************, "Solution72_new21Game_0_2_Test": 1.****************, "Solution16_isUgly_0_0_Test": 1.****************, "Solution28_isPerfectSquare_0_0_Test": 1.****************, "Solution143_reinitializePermutation_0_0_Test": 1.****************, "Solution22_isPowerOfThree_0_0_Test": 1.****************, "Solution86_minAreaRect_0_0_Test": 1.***************, "Solution172_sumOfMultiples_1_0_Test": 1.****************, "Solution107_numberOfSubarrays_0_3_Test": 1.****************, "Solution31_getMoneyAmount_0_0_Test": 1.****************, "Solution11_countPrimes_0_0_Test": 1.****************, "Solution8_maxPoints_0_0_Test": 1.****************, "Solution34_numberOfBoomerangs_0_0_Test": 0.****************, "Solution128_maxCoins_0_0_Test": 1.****************, "Solution39_constructRectangle_0_0_Test": 1.****************, "Solution61_reachNumber_0_0_Test": 1.****************, "Solution0_intToRoman_0_0_Test": 1.3324850901523424, "Solution147_findTheWinner_0_0_Test": 1.1761499700734346, "Solution109_oddCells_0_0_Test": 1.0958087334604958, "Solution48_smallestFactorization_0_1_Test": 1.189954461567789, "Solution144_countNicePairs_0_0_Test": 1.4013965042320562, "Solution38_PredictTheWinner_0_0_Test": 1.441754332224393, "Solution82_superpalindromesInRange_0_0_Test": 1.2442611758965274, "Solution67_similarRGB_0_0_Test": 1.101106260784278, "Solution21_bulbSwitch_0_0_Test": 1.3193339455085333, "Solution2_divide_0_0_Test": 1.4286921040136562, "Solution88_kClosest_0_0_Test": 1.275631897974364, "Solution146_arraySign_0_0_Test": 1.1483291883142233, "Solution44_minDistance_1_0_Test": 1.7900619599076386, "Solution110_numOfBurgers_0_0_Test": 1.3750439801973626, "Solution40_checkPerfectNumber_0_0_Test": 1.2988957843349327, "Solution117_findMinFibonacciNumbers_0_0_Test": 1.1915800635806528, "Solution84_numMusicPlaylists_0_4_Test": 1.5396084637145628, "Solution127_numOfSubarrays_0_4_Test": 1.2575283572784288, "Solution160_smallestEvenMultiple_0_0_Test": 1.3507772060465268, "Solution56_maximumSwap_0_0_Test": 1.2200525182320734, "Solution7_climbStairs_0_0_Test": 1.165073450950711, "Solution119_simplifiedFractions_0_0_Test": 1.4951708012677012, "Solution49_maximumProduct_0_0_Test": 1.1941427609634143, "Solution27_canMeasureWater_0_0_Test": 1.1210099025401081, "Solution99_isArmstrong_0_0_Test": 1.3030527464504496, "Solution106_checkStraightLine_0_0_Test": 1.2072374666237826, "Solution153_winnerOfGame_0_0_Test": 1.1030334006949474, "Solution65_rotatedDigits_0_1_Test": 1.602708978934016, "Solution90_smallestRepunitDivByK_0_0_Test": 1.3503237225257707, "Solution32_fizzBuzz_0_0_Test": 1.290512913916336, "Solution63_numRabbits_0_0_Test": 1.209818252784681, "Solution71_isRectangleOverlap_0_0_Test": 1.2271036934145974, "Solution77_stoneGame_0_0_Test": 1.5738029076317512, "Solution114_sumFourDivisors_0_1_Test": 1.2971565258203885, "Solution105_probabilityOfHeads_0_1_Test": 1.2081522791669577, "Solution41_fib_0_0_Test": 1.4492315194078491, "Solution100_tribonacci_0_0_Test": 1.4349963216109851, "Solution70_consecutiveNumbersSum_0_0_Test": 1.1782896104427398, "Solution66_preimageSizeFZF_0_1_Test": 1.1513727515321668, "Solution98_distributeCandies_0_0_Test": 1.3230548580597774, "Solution50_judgeSquareSum_0_0_Test": 1.311386864333478, "Solution74_mirrorReflection_0_0_Test": 1.297368871277013, "Solution115_countLargestGroup_0_9_Test": 1.3534672891522337, "Solution17_nthUglyNumber_0_0_Test": 1.027698152815764, "Solution46_validSquare_0_0_Test": 1.1928569346496454, "Solution69_largestTriangleArea_0_0_Test": 1.3031726680230031, "Solution19_canWinNim_0_1_Test": 1.1875697988145246, "Solution157_countEven_0_0_Test": 1.4592982040425677, "Solution159_waysToBuyPensPencils_0_8_Test": 1.2282528473655203, "Solution53_newInteger_0_2_Test": 1.5123527368214562, "Solution134_numberOfMatches_0_0_Test": 1.2558182774167848, "Solution36_canIWin_0_0_Test": 1.1392455767514371, "Solution57_flipLights_0_1_Test": 1.63449686948284, "Solution47_maxCount_0_0_Test": 1.3477001377136602, "Solution13_isPowerOfTwo_0_0_Test": 1.0054365026356238, "Solution175_count_0_0_Test": 1.2197181664266685, "Solution37_largestPalindrome_0_0_Test": 1.129676566159259, "Solution113_angleClock_0_0_Test": 1.2215670974790482, "Solution83_hasGroupsSizeX_0_0_Test": 1.2911722493117044, "Solution52_solveEquation_0_0_Test": 1.6284141308139402, "Solution5_uniquePaths_0_0_Test": 1.3307656249548576, "Solution145_countDifferentSubsequenceGCDs_0_0_Test": 1.4160208208648388, "Solution133_stoneGameVI_0_0_Test": 1.2884056475634897, "Solution167_alternateDigitSum_0_0_Test": 1.306682002474788, "Solution135_stoneGameVII_0_0_Test": 1.172561821451703, "Solution89_largestPerimeter_0_0_Test": 1.2579907086983875, "Solution139_countBalls_0_0_Test": 1.103062388352689, "Solution158_sum_0_0_Test": 1.62488679508571, "Solution108_isGoodArray_0_1_Test": 1.2141900519733217, "Solution12_computeArea_0_0_Test": 1.2896984454282634, "Solution101_numPrimeArrangements_0_0_Test": 1.3361063006460479, "Solution152_stoneGameIX_0_2_Test": 1.1089609084832193, "Solution25_integerBreak_0_0_Test": 1.2527868417328285, "Solution9_trailingZeroes_0_0_Test": 1.169391894392682, "Solution96_addNegabinary_0_8_Test": 1.6112712472868473, "Solution123_numIdenticalPairs_0_0_Test": 1.4703846403549246, "Solution131_sumOddLengthSubarrays_0_0_Test": 1.5290420185722815, "Solution129_stoneGameV_0_0_Test": 1.5072283769901098, "Solution121_kthFactor_0_1_Test": 1.1789032889449025, "Solution14_countDigitOne_0_0_Test": 1.245435589512538, "Solution58_selfDividingNumbers_0_0_Test": 1.490210742022463, "Solution33_arrangeCoins_0_0_Test": 1.4713778541958435, "Solution124_getMinDistSum_0_0_Test": 1.2728881629081408, "Solution111_subtractProductAndSum_0_0_Test": 1.2231371362867827, "Solution116_checkOverlap_0_0_Test": 1.2476617090635351, "Solution148_countPoints_0_0_Test": 1.4227543387683068, "Solution161_commonFactors_0_2_Test": 1.2628446180426038, "Solution141_checkPowersOfThree_0_0_Test": 1.2727441901769156, "Solution169_splitNum_0_0_Test": 1.132026129395886, "Solution112_numberOfSteps_0_0_Test": 1.2440154167956723, "Solution42_complexNumberMultiply_0_0_Test": 1.201019381945353, "Solution103_nthPersonGetsNthSeat_0_0_Test": 1.219801211331364, "Solution10_isHappy_1_0_Test": 1.3905828933398365, "Solution55_findKthNumber_0_2_Test": 1.3220548398836474, "Solution162_averageValue_0_0_Test": 0.9869714243132877, "Solution87_powerfulIntegers_0_0_Test": 0.9288985346356643, "Solution80_superEggDrop_0_0_Test": 1.3177406170792711, "Solution4_getPermutation_0_0_Test": 1.1265091472538005, "Solution92_allCellsDistOrder_0_0_Test": 1.420442772455197, "Solution81_surfaceArea_0_0_Test": 1.2805517322230449, "Solution184_maximumPrimeDifference_0_1_Test": 1.2492446736000573, "Solution29_largestDivisibleSubset_0_3_Test": 1.3039624578766387, "Solution151_missingRolls_0_0_Test": 1.5545446418925741, "Solution68_soupServings_0_0_Test": 1.4911936236605288, "Solution62_reachingPoints_0_0_Test": 1.4012902280321318, "Solution132_visiblePoints_0_1_Test": 1.1834866103466883, "Solution59_monotoneIncreasingDigits_0_0_Test": 1.4465492901525705}, "aer": {"Solution78_nthMagicalNumber_0_1_Test": 1.958777879723421, "Solution102_minCostToMoveChips_0_0_Test": 1.5235871002835961, "Solution93_numMovesStones_0_1_Test": 1.9064610001133624, "Solution1_romanToInt_0_0_Test": 2.108171580165504, "Solution126_countOdds_0_9_Test": 2.258078040140571, "Solution91_divisorGame_0_0_Test": 2.5433899244082117, "Solution20_minTotalDistance_0_0_Test": 1.8118040312091444, "Solution30_superPow_0_1_Test": 2.4625419775776614, "Solution122_winnerSquareGame_0_3_Test": 2.96198056274827, "Solution75_primePalindrome_0_0_Test": 2.3399651439238567, "Solution76_reorderedPowerOf2_0_0_Test": 3.656331496428988, "Solution60_countCornerRectangles_0_1_Test": 2.917906449236237, "Solution24_isPowerOfFour_0_0_Test": 1.9007127657518874, "Solution73_numMagicSquaresInside_0_0_Test": 2.0077501597146368, "Solution125_numWaterBottles_0_3_Test": 2.502515653851062, "Solution15_addDigits_0_0_Test": 2.2686335134237834, "Solution79_projectionArea_0_0_Test": 2.5411941851196787, "Solution26_countNumbersWithUniqueDigits_0_0_Test": 2.***************, "Solution166_countDigits_0_1_Test": 1.****************, "Solution35_poorPigs_0_1_Test": 2.***************, "Solution23_isSelfCrossing_0_0_Test": 2.***************, "Solution18_numSquares_0_0_Test": 2.****************, "Solution136_totalMoney_0_0_Test": 2.****************, "Solution183_sumOfTheDigitsOfHarshadNumber_0_0_Test": 1.****************, "Solution6_mySqrt_0_0_Test": 2.****************, "Solution94_isBoomerang_0_0_Test": 2.***************, "Solution179_accountBalanceAfterPurchase_0_2_Test": 2.****************, "Solution104_missingNumber_0_0_Test": 1.****************, "Solution72_new21Game_0_2_Test": 1.****************, "Solution16_isUgly_0_0_Test": 2.***************, "Solution28_isPerfectSquare_0_0_Test": 2.**************, "Solution143_reinitializePermutation_0_0_Test": 2.****************, "Solution22_isPowerOfThree_0_0_Test": 2.****************, "Solution86_minAreaRect_0_0_Test": 2.***************, "Solution172_sumOfMultiples_1_0_Test": 1.****************, "Solution107_numberOfSubarrays_0_3_Test": 3.***************, "Solution31_getMoneyAmount_0_0_Test": 2.***************, "Solution11_countPrimes_0_0_Test": 2.***************, "Solution8_maxPoints_0_0_Test": 2.*************, "Solution34_numberOfBoomerangs_0_0_Test": 2.****************, "Solution128_maxCoins_0_0_Test": 2.****************, "Solution39_constructRectangle_0_0_Test": 2.***************, "Solution61_reachNumber_0_0_Test": 2.***************, "Solution0_intToRoman_0_0_Test": 2.5205178919639217, "Solution147_findTheWinner_0_0_Test": 1.916887262643217, "Solution109_oddCells_0_0_Test": 2.4270325868513014, "Solution48_smallestFactorization_0_1_Test": 1.8989751752076813, "Solution144_countNicePairs_0_0_Test": 2.14922726255723, "Solution38_PredictTheWinner_0_0_Test": 2.0246180417145236, "Solution82_superpalindromesInRange_0_0_Test": 1.822904375235816, "Solution67_similarRGB_0_0_Test": 2.2328462144100736, "Solution21_bulbSwitch_0_0_Test": 2.578845227135318, "Solution2_divide_0_0_Test": 2.2097312790829977, "Solution88_kClosest_0_0_Test": 2.7947381836314604, "Solution146_arraySign_0_0_Test": 1.9600049024108084, "Solution44_minDistance_1_0_Test": 1.5783686182300543, "Solution110_numOfBurgers_0_0_Test": 1.8067847167163544, "Solution40_checkPerfectNumber_0_0_Test": 1.8261411303292805, "Solution117_findMinFibonacciNumbers_0_0_Test": 2.42666751335034, "Solution84_numMusicPlaylists_0_4_Test": 1.724414577411013, "Solution127_numOfSubarrays_0_4_Test": 1.9890517955842042, "Solution160_smallestEvenMultiple_0_0_Test": 1.7544740833505355, "Solution56_maximumSwap_0_0_Test": 1.6500406144867092, "Solution7_climbStairs_0_0_Test": 1.9795552413903286, "Solution119_simplifiedFractions_0_0_Test": 2.530794360831543, "Solution49_maximumProduct_0_0_Test": 2.7522538299375823, "Solution27_canMeasureWater_0_0_Test": 1.9740038070023682, "Solution99_isArmstrong_0_0_Test": 2.2346801967638257, "Solution106_checkStraightLine_0_0_Test": 2.1223751926616434, "Solution153_winnerOfGame_0_0_Test": 2.507320980104343, "Solution65_rotatedDigits_0_1_Test": 2.3566917034191315, "Solution90_smallestRepunitDivByK_0_0_Test": 2.1601866990586447, "Solution32_fizzBuzz_0_0_Test": 2.212043201726325, "Solution63_numRabbits_0_0_Test": 0.7325503297323139, "Solution71_isRectangleOverlap_0_0_Test": 1.8501363610864057, "Solution77_stoneGame_0_0_Test": 2.690505955372579, "Solution114_sumFourDivisors_0_1_Test": 2.3245166178786882, "Solution105_probabilityOfHeads_0_1_Test": 2.236126487113543, "Solution41_fib_0_0_Test": 2.0974782831527037, "Solution100_tribonacci_0_0_Test": 2.1485728534656476, "Solution70_consecutiveNumbersSum_0_0_Test": 0.740786994622967, "Solution66_preimageSizeFZF_0_1_Test": 0.7288973467396809, "Solution98_distributeCandies_0_0_Test": 2.2557649500579524, "Solution50_judgeSquareSum_0_0_Test": 2.406651431350567, "Solution74_mirrorReflection_0_0_Test": 2.4479926661100837, "Solution115_countLargestGroup_0_9_Test": 2.5180448711905354, "Solution17_nthUglyNumber_0_0_Test": 2.2980600011145995, "Solution46_validSquare_0_0_Test": 2.4204088995201403, "Solution69_largestTriangleArea_0_0_Test": 2.3166232346662925, "Solution19_canWinNim_0_1_Test": 2.3425325799132017, "Solution157_countEven_0_0_Test": 2.352139446960841, "Solution159_waysToBuyPensPencils_0_8_Test": 1.6987160843152627, "Solution53_newInteger_0_2_Test": 2.2652987427078943, "Solution134_numberOfMatches_0_0_Test": 0.7396745401994314, "Solution36_canIWin_0_0_Test": 2.361153147608881, "Solution57_flipLights_0_1_Test": 2.2795379128269655, "Solution47_maxCount_0_0_Test": 2.353985944381363, "Solution13_isPowerOfTwo_0_0_Test": 2.061936522996003, "Solution175_count_0_0_Test": 2.3243699712445753, "Solution37_largestPalindrome_0_0_Test": 2.4817125229594925, "Solution113_angleClock_0_0_Test": 2.106843875142682, "Solution83_hasGroupsSizeX_0_0_Test": 0.7311793350103748, "Solution52_solveEquation_0_0_Test": 1.6755702824237735, "Solution5_uniquePaths_0_0_Test": 2.2400779651490463, "Solution145_countDifferentSubsequenceGCDs_0_0_Test": 2.4197942996397237, "Solution133_stoneGameVI_0_0_Test": 2.400973044019853, "Solution167_alternateDigitSum_0_0_Test": 2.1546315217123944, "Solution135_stoneGameVII_0_0_Test": 1.9442347969452771, "Solution89_largestPerimeter_0_0_Test": 0.7303323650100119, "Solution139_countBalls_0_0_Test": 2.0032284788515393, "Solution158_sum_0_0_Test": 2.1543307396404696, "Solution108_isGoodArray_0_1_Test": 2.075327725115027, "Solution12_computeArea_0_0_Test": 2.437833465876061, "Solution101_numPrimeArrangements_0_0_Test": 2.2226927232352596, "Solution152_stoneGameIX_0_2_Test": 2.302646060273992, "Solution25_integerBreak_0_0_Test": 2.0440969813695107, "Solution9_trailingZeroes_0_0_Test": 1.7858858171602017, "Solution96_addNegabinary_0_8_Test": 2.483897325654933, "Solution123_numIdenticalPairs_0_0_Test": 2.3565731233153686, "Solution131_sumOddLengthSubarrays_0_0_Test": 2.350804686745007, "Solution129_stoneGameV_0_0_Test": 2.407655273005033, "Solution121_kthFactor_0_1_Test": 2.339376040635736, "Solution14_countDigitOne_0_0_Test": 2.042273355707282, "Solution58_selfDividingNumbers_0_0_Test": 2.558188370424917, "Solution33_arrangeCoins_0_0_Test": 2.2695306361439673, "Solution124_getMinDistSum_0_0_Test": 2.2329683802996465, "Solution111_subtractProductAndSum_0_0_Test": 2.1369996166150136, "Solution116_checkOverlap_0_0_Test": 2.151216439041, "Solution148_countPoints_0_0_Test": 2.422062280090498, "Solution161_commonFactors_0_2_Test": 2.284492385231129, "Solution141_checkPowersOfThree_0_0_Test": 2.096791363743354, "Solution169_splitNum_0_0_Test": 2.5853421356921005, "Solution112_numberOfSteps_0_0_Test": 2.488737344214768, "Solution42_complexNumberMultiply_0_0_Test": 1.8143551898111228, "Solution103_nthPersonGetsNthSeat_0_0_Test": 2.4192124084154543, "Solution10_isHappy_1_0_Test": 2.3606806899193216, "Solution55_findKthNumber_0_2_Test": 2.0651871735655094, "Solution162_averageValue_0_0_Test": 2.0597228595699875, "Solution87_powerfulIntegers_0_0_Test": 2.503998502180571, "Solution80_superEggDrop_0_0_Test": 1.8078292948029366, "Solution4_getPermutation_0_0_Test": 2.4086480327752335, "Solution92_allCellsDistOrder_0_0_Test": 2.028720200028189, "Solution81_surfaceArea_0_0_Test": 1.762672203560135, "Solution184_maximumPrimeDifference_0_1_Test": 2.2182884617131693, "Solution29_largestDivisibleSubset_0_3_Test": 2.435997232646826, "Solution151_missingRolls_0_0_Test": 2.3232456407863697, "Solution68_soupServings_0_0_Test": 2.201185609366042, "Solution62_reachingPoints_0_0_Test": 2.3803053045704603, "Solution132_visiblePoints_0_1_Test": 2.1536583261142477, "Solution59_monotoneIncreasingDigits_0_0_Test": 2.1726723931148255}, "pds": {"Solution78_nthMagicalNumber_0_1_Test": {"throughput": 0.9172749973275945, "cpu_efficiency": 4.003596371250435, "memory_efficiency": 1.247984421929695, "cache_efficiency": 1.1400380346964016, "branch_prediction_accuracy": 1.041388301863502, "overall_pds": 0.8}, "Solution102_minCostToMoveChips_0_0_Test": {"throughput": 0.9559356857269791, "cpu_efficiency": 2.6638707466114706, "memory_efficiency": 1.1739862045375054, "cache_efficiency": 1.0570472999299405, "branch_prediction_accuracy": 1.0476510490723556, "overall_pds": 0.8}, "Solution93_numMovesStones_0_1_Test": {"throughput": 0.8525544861827414, "cpu_efficiency": 3.907479921974912, "memory_efficiency": 1.283639916639715, "cache_efficiency": 1.0845152299901317, "branch_prediction_accuracy": 1.049519438958189, "overall_pds": 0.8}, "Solution1_romanToInt_0_0_Test": {"throughput": 0.8662187480128725, "cpu_efficiency": 4.608124500746607, "memory_efficiency": 1.2672158225764958, "cache_efficiency": 1.051032815761425, "branch_prediction_accuracy": 1.054993289436554, "overall_pds": 0.8}, "Solution126_countOdds_0_9_Test": {"throughput": 0.8785026296612588, "cpu_efficiency": 5.080575034603828, "memory_efficiency": 1.2614910875422318, "cache_efficiency": 1.0774945160348615, "branch_prediction_accuracy": 1.0514560096670618, "overall_pds": 0.8}, "Solution91_divisorGame_0_0_Test": {"throughput": 0.8800324995628441, "cpu_efficiency": 6.0579224419535995, "memory_efficiency": 1.2457011086748424, "cache_efficiency": 1.0585871125036252, "branch_prediction_accuracy": 1.0709374505979212, "overall_pds": 0.8}, "Solution20_minTotalDistance_0_0_Test": {"throughput": 0.8695102859918075, "cpu_efficiency": 4.622004024005199, "memory_efficiency": 1.2714073267282886, "cache_efficiency": 1.0638683016133041, "branch_prediction_accuracy": 1.0649315590405761, "overall_pds": 0.8}, "Solution30_superPow_0_1_Test": {"throughput": 0.8832676470024697, "cpu_efficiency": 5.769779851492217, "memory_efficiency": 1.249941137832996, "cache_efficiency": 1.0741513056720984, "branch_prediction_accuracy": 1.0598067177013648, "overall_pds": 0.8}, "Solution122_winnerSquareGame_0_3_Test": {"throughput": 0.8536521026813831, "cpu_efficiency": 7.418768571695418, "memory_efficiency": 1.2546124037113844, "cache_efficiency": 1.0864000068978141, "branch_prediction_accuracy": 1.081237527056369, "overall_pds": 0.8}, "Solution75_primePalindrome_0_0_Test": {"throughput": 0.8935406699193618, "cpu_efficiency": 5.371614479834573, "memory_efficiency": 1.2541702434342707, "cache_efficiency": 1.06420447728881, "branch_prediction_accuracy": 1.0668279358454216, "overall_pds": 0.8}, "Solution76_reorderedPowerOf2_0_0_Test": {"throughput": 0.8538704948880146, "cpu_efficiency": 9.72153492316589, "memory_efficiency": 1.2766378107746146, "cache_efficiency": 1.0953969460779933, "branch_prediction_accuracy": 1.0584810079002132, "overall_pds": 0.8}, "Solution60_countCornerRectangles_0_1_Test": {"throughput": 0.878262224437783, "cpu_efficiency": 7.288532504165006, "memory_efficiency": 1.2557131409309823, "cache_efficiency": 1.0755121984196427, "branch_prediction_accuracy": 1.0652512386554545, "overall_pds": 0.8}, "Solution24_isPowerOfFour_0_0_Test": {"throughput": 0.8867658628193246, "cpu_efficiency": 3.891337572215095, "memory_efficiency": 1.2555528310044135, "cache_efficiency": 1.0758031757196969, "branch_prediction_accuracy": 1.0742392288768947, "overall_pds": 0.8}, "Solution73_numMagicSquaresInside_0_0_Test": {"throughput": 0.8665594804994992, "cpu_efficiency": 4.257275285425006, "memory_efficiency": 1.239111499962554, "cache_efficiency": 1.0733527324909735, "branch_prediction_accuracy": 1.0515688854660736, "overall_pds": 0.8}, "Solution125_numWaterBottles_0_3_Test": {"throughput": 0.8540787090077449, "cpu_efficiency": 5.909053839518879, "memory_efficiency": 1.2362929394383058, "cache_efficiency": 1.0628081408996926, "branch_prediction_accuracy": 1.0581242969678932, "overall_pds": 0.8}, "Solution15_addDigits_0_0_Test": {"throughput": 0.8488573603964591, "cpu_efficiency": 5.150720934438499, "memory_efficiency": 1.2878318378189957, "cache_efficiency": 1.0502485262697592, "branch_prediction_accuracy": 1.0593245645284364, "overall_pds": 0.8}, "Solution79_projectionArea_0_0_Test": {"throughput": 0.8875978230572664, "cpu_efficiency": 6.037969480051486, "memory_efficiency": 1.2451042338268532, "cache_efficiency": 1.069683924735475, "branch_prediction_accuracy": 1.0626225531109936, "overall_pds": 0.8}, "Solution26_countNumbersWithUniqueDigits_0_0_Test": {"throughput": 0.8578703007157853, "cpu_efficiency": 4.384732542369329, "memory_efficiency": 1.2378831240092818, "cache_efficiency": 1.0655561703989835, "branch_prediction_accuracy": 1.054194156553212, "overall_pds": 0.8}, "Solution166_countDigits_0_1_Test": {"throughput": 0.8658130010967021, "cpu_efficiency": 4.511000296696743, "memory_efficiency": 1.2767712590024467, "cache_efficiency": 1.0558072963458391, "branch_prediction_accuracy": 1.059649795062786, "overall_pds": 0.8}, "Solution35_poorPigs_0_1_Test": {"throughput": 0.875269910363014, "cpu_efficiency": 5.513144967229724, "memory_efficiency": 1.2393141858622292, "cache_efficiency": 1.0707234677144233, "branch_prediction_accuracy": 1.0677125503996248, "overall_pds": 0.8}, "Solution23_isSelfCrossing_0_0_Test": {"throughput": 0.8815891100052705, "cpu_efficiency": 5.923122412528329, "memory_efficiency": 1.2375182911345382, "cache_efficiency": 1.0600216069416217, "branch_prediction_accuracy": 1.061856167587098, "overall_pds": 0.8}, "Solution18_numSquares_0_0_Test": {"throughput": 0.8461951209246592, "cpu_efficiency": 5.568859454762249, "memory_efficiency": 1.2588544862314601, "cache_efficiency": 1.0616454142394562, "branch_prediction_accuracy": 1.0691121103731258, "overall_pds": 0.8}, "Solution136_totalMoney_0_0_Test": {"throughput": 0.8524526405586388, "cpu_efficiency": 5.182856929524726, "memory_efficiency": 1.25726400206593, "cache_efficiency": 1.0868679081493682, "branch_prediction_accuracy": 1.0590299108160386, "overall_pds": 0.8}, "Solution183_sumOfTheDigitsOfHarshadNumber_0_0_Test": {"throughput": 0.8404988255799994, "cpu_efficiency": 3.5930125420033105, "memory_efficiency": 1.2895434419515328, "cache_efficiency": 1.0610865884043286, "branch_prediction_accuracy": 1.0449665594254107, "overall_pds": 0.8}, "Solution6_mySqrt_0_0_Test": {"throughput": 0.****************, "cpu_efficiency": 5.***************, "memory_efficiency": 1.****************, "cache_efficiency": 1.***************, "branch_prediction_accuracy": 1.****************, "overall_pds": 0.8}, "Solution94_isBoomerang_0_0_Test": {"throughput": 0.***************, "cpu_efficiency": 4.***************, "memory_efficiency": 1.***************, "cache_efficiency": 1.****************, "branch_prediction_accuracy": 1.****************, "overall_pds": 0.8}, "Solution179_accountBalanceAfterPurchase_0_2_Test": {"throughput": 0.***************, "cpu_efficiency": 4.***************, "memory_efficiency": 1.****************, "cache_efficiency": 1.****************, "branch_prediction_accuracy": 1.****************, "overall_pds": 0.8}, "Solution104_missingNumber_0_0_Test": {"throughput": 0.****************, "cpu_efficiency": 4.***************, "memory_efficiency": 1.****************, "cache_efficiency": 1.****************, "branch_prediction_accuracy": 1.****************, "overall_pds": 0.8}, "Solution72_new21Game_0_2_Test": {"throughput": 0.***************, "cpu_efficiency": 4.****************, "memory_efficiency": 1.****************, "cache_efficiency": 1.****************, "branch_prediction_accuracy": 1.****************, "overall_pds": 0.8}, "Solution16_isUgly_0_0_Test": {"throughput": 0.****************, "cpu_efficiency": 4.***************, "memory_efficiency": 1.***************, "cache_efficiency": 1.****************, "branch_prediction_accuracy": 1.0522618953444975, "overall_pds": 0.8}, "Solution28_isPerfectSquare_0_0_Test": {"throughput": 0.9224200859401854, "cpu_efficiency": 4.652569256088868, "memory_efficiency": 1.196220829909522, "cache_efficiency": 1.0605303958947674, "branch_prediction_accuracy": 1.0629177833778332, "overall_pds": 0.8}, "Solution143_reinitializePermutation_0_0_Test": {"throughput": 0.8675834163320376, "cpu_efficiency": 5.569337454360335, "memory_efficiency": 1.2624621077248306, "cache_efficiency": 1.071190610188981, "branch_prediction_accuracy": 1.0537461548663456, "overall_pds": 0.8}, "Solution22_isPowerOfThree_0_0_Test": {"throughput": 0.9087446257209377, "cpu_efficiency": 4.4921063338110425, "memory_efficiency": 1.2140438822403752, "cache_efficiency": 1.059444614386202, "branch_prediction_accuracy": 1.0454657399201683, "overall_pds": 0.8}, "Solution86_minAreaRect_0_0_Test": {"throughput": 0.867026023206539, "cpu_efficiency": 7.159659669189562, "memory_efficiency": 1.2445498405560684, "cache_efficiency": 1.061041595245128, "branch_prediction_accuracy": 1.0611936110211064, "overall_pds": 0.8}, "Solution172_sumOfMultiples_1_0_Test": {"throughput": 0.8632979551407886, "cpu_efficiency": 3.992652114060092, "memory_efficiency": 1.2740282422823102, "cache_efficiency": 1.047979733345019, "branch_prediction_accuracy": 1.0567572860494001, "overall_pds": 0.8}, "Solution107_numberOfSubarrays_0_3_Test": {"throughput": 0.8538968870507944, "cpu_efficiency": 7.953798290341253, "memory_efficiency": 1.2774412425654875, "cache_efficiency": 1.0736165922560805, "branch_prediction_accuracy": 1.0738627209884914, "overall_pds": 0.8}, "Solution31_getMoneyAmount_0_0_Test": {"throughput": 0.8708896982064245, "cpu_efficiency": 5.0305838415849005, "memory_efficiency": 1.2721545073027378, "cache_efficiency": 1.0581964345999706, "branch_prediction_accuracy": 1.0462116758795756, "overall_pds": 0.8}, "Solution11_countPrimes_0_0_Test": {"throughput": 0.9124326799885978, "cpu_efficiency": 6.18414497450512, "memory_efficiency": 1.2278471574907157, "cache_efficiency": 1.083943550891761, "branch_prediction_accuracy": 1.0583593242503015, "overall_pds": 0.8}, "Solution8_maxPoints_0_0_Test": {"throughput": 0.8352554981628587, "cpu_efficiency": 6.469662923677402, "memory_efficiency": 1.2881855525028598, "cache_efficiency": 1.065169614690649, "branch_prediction_accuracy": 1.0653096108385476, "overall_pds": 0.8}, "Solution34_numberOfBoomerangs_0_0_Test": {"throughput": 0.858343138049415, "cpu_efficiency": 5.5841746948583975, "memory_efficiency": 1.2657742926450488, "cache_efficiency": 1.0566743504119882, "branch_prediction_accuracy": 1.0533187350413045, "overall_pds": 0.8}, "Solution128_maxCoins_0_0_Test": {"throughput": 0.8826165824838764, "cpu_efficiency": 5.253666491237294, "memory_efficiency": 1.237724748460387, "cache_efficiency": 1.0658380099504745, "branch_prediction_accuracy": 1.0574002241392935, "overall_pds": 0.8}, "Solution39_constructRectangle_0_0_Test": {"throughput": 0.8628484589725164, "cpu_efficiency": 5.0372079637667895, "memory_efficiency": 1.272296315394704, "cache_efficiency": 1.0603672732215512, "branch_prediction_accuracy": 1.0613028948937338, "overall_pds": 0.8}, "Solution61_reachNumber_0_0_Test": {"throughput": 0.9093365292344975, "cpu_efficiency": 4.532691633524061, "memory_efficiency": 1.2002879446137025, "cache_efficiency": 1.07272619360942, "branch_prediction_accuracy": 1.0551835800125595, "overall_pds": 0.8}, "Solution0_intToRoman_0_0_Test": {"throughput": 0.8627137174619417, "cpu_efficiency": 5.952229756962008, "memory_efficiency": 1.2645609431180604, "cache_efficiency": 1.0799901286164473, "branch_prediction_accuracy": 1.0617440756069596, "overall_pds": 0.8}, "Solution147_findTheWinner_0_0_Test": {"throughput": 0.8669587905211991, "cpu_efficiency": 3.9654839091776215, "memory_efficiency": 1.282022570374775, "cache_efficiency": 1.0620947147779773, "branch_prediction_accuracy": 1.0690540382264122, "overall_pds": 0.8}, "Solution109_oddCells_0_0_Test": {"throughput": 0.8888901685684588, "cpu_efficiency": 5.658403450683478, "memory_efficiency": 1.2353530233723329, "cache_efficiency": 1.0683294820860652, "branch_prediction_accuracy": 1.0629557578998268, "overall_pds": 0.8}, "Solution48_smallestFactorization_0_1_Test": {"throughput": 0.8754271850761094, "cpu_efficiency": 3.9107906917106408, "memory_efficiency": 1.2334878247408025, "cache_efficiency": 1.0551911546372446, "branch_prediction_accuracy": 1.0534848126292462, "overall_pds": 0.8}, "Solution144_countNicePairs_0_0_Test": {"throughput": 0.9015037091667364, "cpu_efficiency": 4.7090893610141435, "memory_efficiency": 1.2317968343701238, "cache_efficiency": 1.0849921004777954, "branch_prediction_accuracy": 1.0783313180028598, "overall_pds": 0.8}, "Solution38_PredictTheWinner_0_0_Test": {"throughput": 0.8680328520891871, "cpu_efficiency": 4.332722812159899, "memory_efficiency": 1.2473863032612338, "cache_efficiency": 1.0559817199058144, "branch_prediction_accuracy": 1.0610242878751424, "overall_pds": 0.8}, "Solution82_superpalindromesInRange_0_0_Test": {"throughput": 0.8757423610038108, "cpu_efficiency": 3.657763599039224, "memory_efficiency": 1.2578379613341526, "cache_efficiency": 1.0596742283812923, "branch_prediction_accuracy": 1.0641338594634526, "overall_pds": 0.8}, "Solution67_similarRGB_0_0_Test": {"throughput": 0.891648610284563, "cpu_efficiency": 5.010578822084997, "memory_efficiency": 1.2373073220998922, "cache_efficiency": 1.0686096428478842, "branch_prediction_accuracy": 1.0758457237657248, "overall_pds": 0.8}, "Solution21_bulbSwitch_0_0_Test": {"throughput": 0.8541967818503661, "cpu_efficiency": 6.162760606557067, "memory_efficiency": 1.2798663419079819, "cache_efficiency": 1.0741864874545872, "branch_prediction_accuracy": 1.0775956832112925, "overall_pds": 0.8}, "Solution2_divide_0_0_Test": {"throughput": 0.8707761983018942, "cpu_efficiency": 4.9454556156717135, "memory_efficiency": 1.2548245273282204, "cache_efficiency": 1.0599770850922785, "branch_prediction_accuracy": 1.0787872922240862, "overall_pds": 0.8}, "Solution88_kClosest_0_0_Test": {"throughput": 0.8382969690866026, "cpu_efficiency": 6.86138658054224, "memory_efficiency": 1.2977267115188051, "cache_efficiency": 1.0883700900260511, "branch_prediction_accuracy": 1.0779986673513804, "overall_pds": 0.8}, "Solution146_arraySign_0_0_Test": {"throughput": 0.8501430981519991, "cpu_efficiency": 4.1148284949735965, "memory_efficiency": 1.2837076619287633, "cache_efficiency": 1.0620322954458465, "branch_prediction_accuracy": 1.0762899632974448, "overall_pds": 0.8}, "Solution44_minDistance_1_0_Test": {"throughput": 0.8533395467702755, "cpu_efficiency": 3.8572527684278595, "memory_efficiency": 1.2765117931970784, "cache_efficiency": 1.0534457120000869, "branch_prediction_accuracy": 1.0511284890795247, "overall_pds": 0.8}, "Solution110_numOfBurgers_0_0_Test": {"throughput": 0.9131043117517487, "cpu_efficiency": 3.599943099395729, "memory_efficiency": 1.1950017528235772, "cache_efficiency": 1.0577544609062517, "branch_prediction_accuracy": 1.0544400168920751, "overall_pds": 0.8}, "Solution40_checkPerfectNumber_0_0_Test": {"throughput": 0.8524018174037802, "cpu_efficiency": 3.6618031023871747, "memory_efficiency": 1.2822484571607409, "cache_efficiency": 1.0652958568568358, "branch_prediction_accuracy": 1.0350290864449923, "overall_pds": 0.8}, "Solution117_findMinFibonacciNumbers_0_0_Test": {"throughput": 0.8333523771411443, "cpu_efficiency": 5.604287051333803, "memory_efficiency": 1.281155976828499, "cache_efficiency": 1.1067715804110922, "branch_prediction_accuracy": 1.0644292115578782, "overall_pds": 0.8}, "Solution84_numMusicPlaylists_0_4_Test": {"throughput": 0.8966650109651786, "cpu_efficiency": 3.338400070185268, "memory_efficiency": 1.2432223971589558, "cache_efficiency": 1.0527265211244559, "branch_prediction_accuracy": 1.0452899068366628, "overall_pds": 0.8}, "Solution127_numOfSubarrays_0_4_Test": {"throughput": 0.9199250322162412, "cpu_efficiency": 5.2088466332506655, "memory_efficiency": 1.2164301207691215, "cache_efficiency": 1.0669528022244914, "branch_prediction_accuracy": 1.041108313412044, "overall_pds": 0.8}, "Solution160_smallestEvenMultiple_0_0_Test": {"throughput": 0.8924857484351688, "cpu_efficiency": 3.3876539915090236, "memory_efficiency": 1.2388106754950472, "cache_efficiency": 1.0893413811016457, "branch_prediction_accuracy": 1.0656654700125947, "overall_pds": 0.8}, "Solution56_maximumSwap_0_0_Test": {"throughput": 0.912238760704332, "cpu_efficiency": 3.0760155146123394, "memory_efficiency": 1.2111723039923732, "cache_efficiency": 1.0601659913484522, "branch_prediction_accuracy": 1.0462227185721427, "overall_pds": 0.8}, "Solution7_climbStairs_0_0_Test": {"throughput": 0.8729937830382177, "cpu_efficiency": 4.16382948023283, "memory_efficiency": 1.2393363544324847, "cache_efficiency": 1.0677178344431304, "branch_prediction_accuracy": 1.0501928573274595, "overall_pds": 0.8}, "Solution119_simplifiedFractions_0_0_Test": {"throughput": 0.8372076948605017, "cpu_efficiency": 6.031021224474246, "memory_efficiency": 1.2911490786246187, "cache_efficiency": 1.0461846016073844, "branch_prediction_accuracy": 1.0708227297756192, "overall_pds": 0.8}, "Solution49_maximumProduct_0_0_Test": {"throughput": 0.8585403865118395, "cpu_efficiency": 6.71158738257585, "memory_efficiency": 1.2646969521275455, "cache_efficiency": 1.0870576032375239, "branch_prediction_accuracy": 1.0652952319804068, "overall_pds": 0.8}, "Solution27_canMeasureWater_0_0_Test": {"throughput": 0.862666554084676, "cpu_efficiency": 5.182721680307159, "memory_efficiency": 1.2819195787882072, "cache_efficiency": 1.048340401320541, "branch_prediction_accuracy": 1.042946514509074, "overall_pds": 0.8}, "Solution99_isArmstrong_0_0_Test": {"throughput": 0.9114462851889205, "cpu_efficiency": 5.024759117682616, "memory_efficiency": 1.239114982777376, "cache_efficiency": 1.0634911385925265, "branch_prediction_accuracy": 1.0727410372981856, "overall_pds": 0.8}, "Solution106_checkStraightLine_0_0_Test": {"throughput": 0.8541078962382062, "cpu_efficiency": 5.669889555849556, "memory_efficiency": 1.28218333429033, "cache_efficiency": 1.0540166923404495, "branch_prediction_accuracy": 1.0567107881041438, "overall_pds": 0.8}, "Solution153_winnerOfGame_0_0_Test": {"throughput": 0.8446194112261356, "cpu_efficiency": 5.936335302534252, "memory_efficiency": 1.285704399201534, "cache_efficiency": 1.0604315976981065, "branch_prediction_accuracy": 1.0499948888731847, "overall_pds": 0.8}, "Solution65_rotatedDigits_0_1_Test": {"throughput": 0.8713165284583401, "cpu_efficiency": 5.436173432504427, "memory_efficiency": 1.264460054634385, "cache_efficiency": 1.0596300668888614, "branch_prediction_accuracy": 1.0622860450623357, "overall_pds": 0.8}, "Solution90_smallestRepunitDivByK_0_0_Test": {"throughput": 0.8604534287780715, "cpu_efficiency": 4.749358394184746, "memory_efficiency": 1.2783025882276446, "cache_efficiency": 1.0866813638492794, "branch_prediction_accuracy": 1.052918963217436, "overall_pds": 0.8}, "Solution32_fizzBuzz_0_0_Test": {"throughput": 0.9116436174766487, "cpu_efficiency": 4.943339143284257, "memory_efficiency": 1.2301213349564675, "cache_efficiency": 1.0671605573290444, "branch_prediction_accuracy": 1.0605851193306717, "overall_pds": 0.8}, "Solution63_numRabbits_0_0_Test": {"throughput": 0.8777217817582955, "cpu_efficiency": 0.0, "memory_efficiency": 1.271743782276297, "cache_efficiency": 1.0788635996671065, "branch_prediction_accuracy": 1.0470903737401482, "overall_pds": 0.6}, "Solution71_isRectangleOverlap_0_0_Test": {"throughput": 0.8506253938724581, "cpu_efficiency": 4.736553926334398, "memory_efficiency": 1.2963993841765324, "cache_efficiency": 1.0738957447023405, "branch_prediction_accuracy": 1.049917149303018, "overall_pds": 0.8}, "Solution77_stoneGame_0_0_Test": {"throughput": 0.898625078278707, "cpu_efficiency": 6.522309198388814, "memory_efficiency": 1.2276970035924952, "cache_efficiency": 1.0809605819290589, "branch_prediction_accuracy": 1.0729705271065852, "overall_pds": 0.8}, "Solution114_sumFourDivisors_0_1_Test": {"throughput": 0.8696419966901547, "cpu_efficiency": 5.295715902789316, "memory_efficiency": 1.225829933173877, "cache_efficiency": 1.0802573341294877, "branch_prediction_accuracy": 1.0539242505892357, "overall_pds": 0.8}, "Solution105_probabilityOfHeads_0_1_Test": {"throughput": 0.8727958661276651, "cpu_efficiency": 5.029621232202156, "memory_efficiency": 1.2472948262381067, "cache_efficiency": 1.0636616138694095, "branch_prediction_accuracy": 1.0430090895433253, "overall_pds": 0.8}, "Solution41_fib_0_0_Test": {"throughput": 0.8513306505681333, "cpu_efficiency": 4.539474708715347, "memory_efficiency": 1.270763896276827, "cache_efficiency": 1.0855911910488956, "branch_prediction_accuracy": 1.0762040421820205, "overall_pds": 0.8}, "Solution100_tribonacci_0_0_Test": {"throughput": 0.8933207036606381, "cpu_efficiency": 4.748896848109339, "memory_efficiency": 1.2298311940371378, "cache_efficiency": 1.0562257397050354, "branch_prediction_accuracy": 1.0455902473919378, "overall_pds": 0.8}, "Solution70_consecutiveNumbersSum_0_0_Test": {"throughput": 0.8545315588974406, "cpu_efficiency": 0.0, "memory_efficiency": 1.258758080796169, "cache_efficiency": 1.1002092728256634, "branch_prediction_accuracy": 1.0511612836852835, "overall_pds": 0.6}, "Solution66_preimageSizeFZF_0_1_Test": {"throughput": 0.8309231214722189, "cpu_efficiency": 0.0, "memory_efficiency": 1.26233248467417, "cache_efficiency": 1.068736080587484, "branch_prediction_accuracy": 1.0686342374328783, "overall_pds": 0.6}, "Solution98_distributeCandies_0_0_Test": {"throughput": 0.8971450989455814, "cpu_efficiency": 5.090452339632454, "memory_efficiency": 1.254372138914548, "cache_efficiency": 1.0716526695382735, "branch_prediction_accuracy": 1.06794301135287, "overall_pds": 0.8}, "Solution50_judgeSquareSum_0_0_Test": {"throughput": 0.9037968429846314, "cpu_efficiency": 5.594479525697871, "memory_efficiency": 1.18911520200533, "cache_efficiency": 1.0654239467042819, "branch_prediction_accuracy": 1.0750550850511056, "overall_pds": 0.8}, "Solution74_mirrorReflection_0_0_Test": {"throughput": 0.87693967067356, "cpu_efficiency": 5.735106775997449, "memory_efficiency": 1.2636550859973301, "cache_efficiency": 1.0643727221897537, "branch_prediction_accuracy": 1.0546254021603736, "overall_pds": 0.8}, "Solution115_countLargestGroup_0_9_Test": {"throughput": 0.8343925537447046, "cpu_efficiency": 5.941877256539144, "memory_efficiency": 1.291062710495719, "cache_efficiency": 1.0849982612717781, "branch_prediction_accuracy": 1.0964308906803326, "overall_pds": 0.8}, "Solution17_nthUglyNumber_0_0_Test": {"throughput": 0.8947819784950357, "cpu_efficiency": 6.230968460803594, "memory_efficiency": 1.2384037239681724, "cache_efficiency": 1.0726778418072687, "branch_prediction_accuracy": 1.0862642830695965, "overall_pds": 0.8}, "Solution46_validSquare_0_0_Test": {"throughput": 0.891129543206238, "cpu_efficiency": 5.649153873656675, "memory_efficiency": 1.2509323633671898, "cache_efficiency": 1.0565460656342203, "branch_prediction_accuracy": 1.0676053100184193, "overall_pds": 0.8}, "Solution69_largestTriangleArea_0_0_Test": {"throughput": 0.8603451121915301, "cpu_efficiency": 5.292004075420516, "memory_efficiency": 1.287441725120361, "cache_efficiency": 1.0679129566954977, "branch_prediction_accuracy": 1.0732420013434627, "overall_pds": 0.8}, "Solution19_canWinNim_0_1_Test": {"throughput": 0.872383172989056, "cpu_efficiency": 5.377593084691701, "memory_efficiency": 1.247505183781203, "cache_efficiency": 1.0660558937023623, "branch_prediction_accuracy": 1.0612356881079716, "overall_pds": 0.8}, "Solution157_countEven_0_0_Test": {"throughput": 0.8929228149733366, "cpu_efficiency": 5.41885488962059, "memory_efficiency": 1.2357560656357145, "cache_efficiency": 1.0571502520731777, "branch_prediction_accuracy": 1.0538003581190956, "overall_pds": 0.8}, "Solution159_waysToBuyPensPencils_0_8_Test": {"throughput": 0.8826961623976014, "cpu_efficiency": 3.2489030511582655, "memory_efficiency": 1.2396340230480232, "cache_efficiency": 1.0548947251688472, "branch_prediction_accuracy": 1.0582543027764326, "overall_pds": 0.8}, "Solution53_newInteger_0_2_Test": {"throughput": 0.8953570709508947, "cpu_efficiency": 5.138099778555694, "memory_efficiency": 1.226558343833366, "cache_efficiency": 1.0528508113667148, "branch_prediction_accuracy": 1.068885967171696, "overall_pds": 0.8}, "Solution134_numberOfMatches_0_0_Test": {"throughput": 0.8374502338218022, "cpu_efficiency": 0.0, "memory_efficiency": 1.26921931352806, "cache_efficiency": 1.0935116826461369, "branch_prediction_accuracy": 1.0622777416841287, "overall_pds": 0.6}, "Solution36_canIWin_0_0_Test": {"throughput": 0.8798624549126425, "cpu_efficiency": 5.4304477076160635, "memory_efficiency": 1.2717375147437997, "cache_efficiency": 1.0616478856762852, "branch_prediction_accuracy": 1.0704456478578837, "overall_pds": 0.8}, "Solution57_flipLights_0_1_Test": {"throughput": 0.8750929493252402, "cpu_efficiency": 5.1824264319202875, "memory_efficiency": 1.2637530304560871, "cache_efficiency": 1.0581283696880692, "branch_prediction_accuracy": 1.0477319843831174, "overall_pds": 0.8}, "Solution47_maxCount_0_0_Test": {"throughput": 0.8816921586510228, "cpu_efficiency": 5.424180161702659, "memory_efficiency": 1.2294264741274095, "cache_efficiency": 1.0598947470427693, "branch_prediction_accuracy": 1.0569589672777906, "overall_pds": 0.8}, "Solution13_isPowerOfTwo_0_0_Test": {"throughput": 0.8618441405314379, "cpu_efficiency": 4.453053697748508, "memory_efficiency": 1.2658291755894349, "cache_efficiency": 1.058776091265301, "branch_prediction_accuracy": 1.0675353433658135, "overall_pds": 0.8}, "Solution175_count_0_0_Test": {"throughput": 0.88555445605639, "cpu_efficiency": 5.34132647466407, "memory_efficiency": 1.249288293294641, "cache_efficiency": 1.0514280476842877, "branch_prediction_accuracy": 1.0598820833415943, "overall_pds": 0.8}, "Solution37_largestPalindrome_0_0_Test": {"throughput": 0.8843834638144243, "cpu_efficiency": 5.828253612256192, "memory_efficiency": 1.2477018889402844, "cache_efficiency": 1.0771867665480743, "branch_prediction_accuracy": 1.0786513749039375, "overall_pds": 0.8}, "Solution113_angleClock_0_0_Test": {"throughput": 0.9017937309269658, "cpu_efficiency": 4.58806364398942, "memory_efficiency": 1.2466069763391192, "cache_efficiency": 1.0690439358462314, "branch_prediction_accuracy": 1.08230142176171, "overall_pds": 0.8}, "Solution83_hasGroupsSizeX_0_0_Test": {"throughput": 0.8697318013710851, "cpu_efficiency": 0.0, "memory_efficiency": 1.23903791397839, "cache_efficiency": 1.0728406874464735, "branch_prediction_accuracy": 1.05350340072601, "overall_pds": 0.6}, "Solution52_solveEquation_0_0_Test": {"throughput": 0.8846921860584002, "cpu_efficiency": 4.150336283241614, "memory_efficiency": 1.2323531699315837, "cache_efficiency": 1.0773995231055065, "branch_prediction_accuracy": 1.0574413960606592, "overall_pds": 0.8}, "Solution5_uniquePaths_0_0_Test": {"throughput": 0.9018295062424189, "cpu_efficiency": 5.056421879113245, "memory_efficiency": 1.2196532237279099, "cache_efficiency": 1.0511187779665356, "branch_prediction_accuracy": 1.0632888524672233, "overall_pds": 0.8}, "Solution145_countDifferentSubsequenceGCDs_0_0_Test": {"throughput": 0.5147588433536416, "cpu_efficiency": 5.910874901263644, "memory_efficiency": 1.7711273366419658, "cache_efficiency": 0.8603227833481205, "branch_prediction_accuracy": 1.0742243397907292, "overall_pds": 0.6}, "Solution133_stoneGameVI_0_0_Test": {"throughput": 0.8648818108795723, "cpu_efficiency": 5.565403426624556, "memory_efficiency": 1.2445884184013356, "cache_efficiency": 1.0759786177304986, "branch_prediction_accuracy": 1.064458754325936, "overall_pds": 0.8}, "Solution167_alternateDigitSum_0_0_Test": {"throughput": 0.8637057689436387, "cpu_efficiency": 5.741443926900885, "memory_efficiency": 1.265116470692746, "cache_efficiency": 1.0815244772768666, "branch_prediction_accuracy": 1.0731743991120526, "overall_pds": 0.8}, "Solution135_stoneGameVII_0_0_Test": {"throughput": 0.868462838912231, "cpu_efficiency": 4.062038694570784, "memory_efficiency": 1.2452122895016762, "cache_efficiency": 1.0555173847779884, "branch_prediction_accuracy": 1.0520324159467667, "overall_pds": 0.8}, "Solution89_largestPerimeter_0_0_Test": {"throughput": 0.8779765686079931, "cpu_efficiency": 0.0, "memory_efficiency": 1.2540044105660664, "cache_efficiency": 1.0725218380305719, "branch_prediction_accuracy": 1.0608614741113547, "overall_pds": 0.6}, "Solution139_countBalls_0_0_Test": {"throughput": 0.9576750389021627, "cpu_efficiency": 4.220806781824189, "memory_efficiency": 1.1575968113057375, "cache_efficiency": 1.0808274313313864, "branch_prediction_accuracy": 1.0640554977349315, "overall_pds": 0.8}, "Solution158_sum_0_0_Test": {"throughput": 0.9112135696088701, "cpu_efficiency": 4.775317508904735, "memory_efficiency": 1.1900732144852328, "cache_efficiency": 1.0504689056326302, "branch_prediction_accuracy": 1.0518551135297676, "overall_pds": 0.8}, "Solution108_isGoodArray_0_1_Test": {"throughput": 0.8421828133917254, "cpu_efficiency": 4.50824204238787, "memory_efficiency": 1.2526340301229453, "cache_efficiency": 1.0536732159633764, "branch_prediction_accuracy": 1.0611166529324678, "overall_pds": 0.8}, "Solution12_computeArea_0_0_Test": {"throughput": 0.8826950372102255, "cpu_efficiency": 5.673916056607815, "memory_efficiency": 1.2371115223583453, "cache_efficiency": 1.0829623215135553, "branch_prediction_accuracy": 1.0703242064416303, "overall_pds": 0.8}, "Solution101_numPrimeArrangements_0_0_Test": {"throughput": 0.8647699004741601, "cpu_efficiency": 4.987511483856714, "memory_efficiency": 1.2745215978690478, "cache_efficiency": 1.057268608997234, "branch_prediction_accuracy": 1.0538987699260751, "overall_pds": 0.8}, "Solution152_stoneGameIX_0_2_Test": {"throughput": 0.8812495756959968, "cpu_efficiency": 5.231560104653124, "memory_efficiency": 1.2590582751962687, "cache_efficiency": 1.074608884843416, "branch_prediction_accuracy": 1.0772475867444735, "overall_pds": 0.8}, "Solution25_integerBreak_0_0_Test": {"throughput": 0.8804878476102086, "cpu_efficiency": 4.36043519238489, "memory_efficiency": 1.251373977218329, "cache_efficiency": 1.0861186817043602, "branch_prediction_accuracy": 1.0515384395563887, "overall_pds": 0.8}, "Solution9_trailingZeroes_0_0_Test": {"throughput": 0.8713844928112974, "cpu_efficiency": 3.531710202940414, "memory_efficiency": 1.2318643965715703, "cache_efficiency": 1.0607356502617518, "branch_prediction_accuracy": 1.0581146364985696, "overall_pds": 0.8}, "Solution96_addNegabinary_0_8_Test": {"throughput": 0.8640534606237255, "cpu_efficiency": 5.811747655268365, "memory_efficiency": 1.2732305282017464, "cache_efficiency": 1.0953840750944708, "branch_prediction_accuracy": 1.0820252993680992, "overall_pds": 0.8}, "Solution123_numIdenticalPairs_0_0_Test": {"throughput": 0.9063023025220379, "cpu_efficiency": 5.409325934632291, "memory_efficiency": 1.2204143726895529, "cache_efficiency": 1.0754022935580336, "branch_prediction_accuracy": 1.0776601800219716, "overall_pds": 0.8}, "Solution131_sumOddLengthSubarrays_0_0_Test": {"throughput": 0.9043315452029934, "cpu_efficiency": 5.377152643385327, "memory_efficiency": 1.223135058822967, "cache_efficiency": 1.0859104172420326, "branch_prediction_accuracy": 1.0779948029291333, "overall_pds": 0.8}, "Solution129_stoneGameV_0_0_Test": {"throughput": 0.8941635845298819, "cpu_efficiency": 5.559720486421828, "memory_efficiency": 1.226049639270215, "cache_efficiency": 1.0942830379744455, "branch_prediction_accuracy": 1.0716445253227533, "overall_pds": 0.8}, "Solution121_kthFactor_0_1_Test": {"throughput": 0.8604339525913183, "cpu_efficiency": 5.376828524346871, "memory_efficiency": 1.2481690193339008, "cache_efficiency": 1.0654273254249857, "branch_prediction_accuracy": 1.0692896840707529, "overall_pds": 0.8}, "Solution14_countDigitOne_0_0_Test": {"throughput": 0.8893505805344089, "cpu_efficiency": 4.377327524246776, "memory_efficiency": 1.2541414839877458, "cache_efficiency": 1.066078486710756, "branch_prediction_accuracy": 1.0461447861143551, "overall_pds": 0.8}, "Solution58_selfDividingNumbers_0_0_Test": {"throughput": 0.8895534152512616, "cpu_efficiency": 6.106342778259939, "memory_efficiency": 1.2397502449714364, "cache_efficiency": 1.0594012743278713, "branch_prediction_accuracy": 1.0552223406357164, "overall_pds": 0.8}, "Solution33_arrangeCoins_0_0_Test": {"throughput": 0.862638160264893, "cpu_efficiency": 5.155372859405178, "memory_efficiency": 1.2840211160678738, "cache_efficiency": 1.0554016328358362, "branch_prediction_accuracy": 1.0486631556898403, "overall_pds": 0.8}, "Solution124_getMinDistSum_0_0_Test": {"throughput": 0.8710543553920582, "cpu_efficiency": 5.0219077458832295, "memory_efficiency": 1.2947012334692973, "cache_efficiency": 1.06056566238648, "branch_prediction_accuracy": 1.0653211970728689, "overall_pds": 0.8}, "Solution111_subtractProductAndSum_0_0_Test": {"throughput": 0.8725948760300021, "cpu_efficiency": 4.686535781483754, "memory_efficiency": 1.2584817708109555, "cache_efficiency": 1.0698191364159169, "branch_prediction_accuracy": 1.0560788714647233, "overall_pds": 0.8}, "Solution116_checkOverlap_0_0_Test": {"throughput": 0.9014713682231008, "cpu_efficiency": 4.743197745161604, "memory_efficiency": 1.2241755445118607, "cache_efficiency": 1.0641733582369437, "branch_prediction_accuracy": 1.0607114773187496, "overall_pds": 0.8}, "Solution148_countPoints_0_0_Test": {"throughput": 0.888608534011645, "cpu_efficiency": 5.631550005832179, "memory_efficiency": 1.2363406854640646, "cache_efficiency": 1.0766536696229825, "branch_prediction_accuracy": 1.0644589223500789, "overall_pds": 0.8}, "Solution161_commonFactors_0_2_Test": {"throughput": 0.875954964706107, "cpu_efficiency": 5.1879971191429775, "memory_efficiency": 1.2624829551610635, "cache_efficiency": 1.0643958358992553, "branch_prediction_accuracy": 1.0531231497981655, "overall_pds": 0.8}, "Solution141_checkPowersOfThree_0_0_Test": {"throughput": 0.8722274649097291, "cpu_efficiency": 4.575893302626829, "memory_efficiency": 1.2706965419582092, "cache_efficiency": 1.056355444937107, "branch_prediction_accuracy": 1.0434850875481962, "overall_pds": 0.8}, "Solution169_splitNum_0_0_Test": {"throughput": 0.8643092427348282, "cpu_efficiency": 6.208768426824628, "memory_efficiency": 1.281551619999214, "cache_efficiency": 1.0485775960356425, "branch_prediction_accuracy": 1.0646708271838885, "overall_pds": 0.8}, "Solution112_numberOfSteps_0_0_Test": {"throughput": 0.8226404404785956, "cpu_efficiency": 5.865713976775604, "memory_efficiency": 1.3008558259095229, "cache_efficiency": 1.0646293638647535, "branch_prediction_accuracy": 1.068340281276926, "overall_pds": 0.8}, "Solution42_complexNumberMultiply_0_0_Test": {"throughput": 0.8827347940189151, "cpu_efficiency": 3.6236418001873583, "memory_efficiency": 1.243963014629267, "cache_efficiency": 1.0645342351063876, "branch_prediction_accuracy": 1.0487778990233205, "overall_pds": 0.8}, "Solution103_nthPersonGetsNthSeat_0_0_Test": {"throughput": 0.8712454339587983, "cpu_efficiency": 5.652585514530023, "memory_efficiency": 1.2414558469216856, "cache_efficiency": 1.0518383633722743, "branch_prediction_accuracy": 1.0526866128107002, "overall_pds": 0.8}, "Solution10_isHappy_1_0_Test": {"throughput": 0.8716162539316656, "cpu_efficiency": 5.450897674775189, "memory_efficiency": 1.2301885517022497, "cache_efficiency": 1.0573500281768455, "branch_prediction_accuracy": 1.0435646037021262, "overall_pds": 0.8}, "Solution55_findKthNumber_0_2_Test": {"throughput": 0.8906942177112476, "cpu_efficiency": 4.47416483310302, "memory_efficiency": 1.2142916914362747, "cache_efficiency": 1.0502263074997429, "branch_prediction_accuracy": 1.0545960541721204, "overall_pds": 0.8}, "Solution162_averageValue_0_0_Test": {"throughput": 0.8985943184122567, "cpu_efficiency": 4.435025702227939, "memory_efficiency": 1.2133205944023375, "cache_efficiency": 1.067250209081175, "branch_prediction_accuracy": 1.0635666761712228, "overall_pds": 0.8}, "Solution87_powerfulIntegers_0_0_Test": {"throughput": 0.846998582226345, "cpu_efficiency": 5.9188382074731, "memory_efficiency": 1.271006139112279, "cache_efficiency": 1.0684605072710358, "branch_prediction_accuracy": 1.0713599136547622, "overall_pds": 0.8}, "Solution80_superEggDrop_0_0_Test": {"throughput": 0.884280323324533, "cpu_efficiency": 3.6193433466722156, "memory_efficiency": 1.214172966040974, "cache_efficiency": 1.0509271078589948, "branch_prediction_accuracy": 1.0454246922357293, "overall_pds": 0.8}, "Solution4_getPermutation_0_0_Test": {"throughput": 0.874913779337768, "cpu_efficiency": 5.597801188351811, "memory_efficiency": 1.246931863619586, "cache_efficiency": 1.0643747667269354, "branch_prediction_accuracy": 1.0525890542948453, "overall_pds": 0.8}, "Solution92_allCellsDistOrder_0_0_Test": {"throughput": 0.8551859357316025, "cpu_efficiency": 4.336124129852409, "memory_efficiency": 1.2752450604141754, "cache_efficiency": 1.0641671027445478, "branch_prediction_accuracy": 1.0454084486403767, "overall_pds": 0.8}, "Solution81_surfaceArea_0_0_Test": {"throughput": 0.893601257729159, "cpu_efficiency": 4.463134075984791, "memory_efficiency": 1.2315056852952133, "cache_efficiency": 1.0600243104548799, "branch_prediction_accuracy": 1.056795429307209, "overall_pds": 0.8}, "Solution184_maximumPrimeDifference_0_1_Test": {"throughput": 0.8481277156050888, "cpu_efficiency": 4.9578295818349645, "memory_efficiency": 1.3093398912160978, "cache_efficiency": 1.071854659623487, "branch_prediction_accuracy": 1.0599825866281662, "overall_pds": 0.8}, "Solution29_largestDivisibleSubset_0_3_Test": {"throughput": 0.8471121893006195, "cpu_efficiency": 5.704475663725112, "memory_efficiency": 1.2822758655460786, "cache_efficiency": 1.0560942048885331, "branch_prediction_accuracy": 1.069001503968359, "overall_pds": 0.8}, "Solution151_missingRolls_0_0_Test": {"throughput": 0.8755797383004361, "cpu_efficiency": 5.301991580404273, "memory_efficiency": 1.2483985716274173, "cache_efficiency": 1.073171671561048, "branch_prediction_accuracy": 1.0666707691349457, "overall_pds": 0.8}, "Solution68_soupServings_0_0_Test": {"throughput": 0.8634686271048425, "cpu_efficiency": 4.9045308185233925, "memory_efficiency": 1.2827561241408656, "cache_efficiency": 1.0681888441066618, "branch_prediction_accuracy": 1.0662019502437579, "overall_pds": 0.8}, "Solution62_reachingPoints_0_0_Test": {"throughput": 0.9134480529293733, "cpu_efficiency": 5.49723461300568, "memory_efficiency": 1.1946576561075661, "cache_efficiency": 1.0707700395829844, "branch_prediction_accuracy": 1.0794945713303739, "overall_pds": 0.8}, "Solution132_visiblePoints_0_1_Test": {"throughput": 0.8425382905523675, "cpu_efficiency": 4.753293210042945, "memory_efficiency": 1.3001951368631157, "cache_efficiency": 1.063084188628309, "branch_prediction_accuracy": 1.0488509600544538, "overall_pds": 0.8}, "Solution59_monotoneIncreasingDigits_0_0_Test": {"throughput": 0.9047502383653216, "cpu_efficiency": 4.806317354585677, "memory_efficiency": 1.241202945131076, "cache_efficiency": 1.0736116226521917, "branch_prediction_accuracy": 1.0460750437097373, "overall_pds": 0.8}}}}