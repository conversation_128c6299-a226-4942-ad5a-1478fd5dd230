================================================================================
SCIENTIFIC PERFORMANCE ANALYSIS REPORT
================================================================================
Comparison: Copilot vs Ground Truth
Analysis Date: 2025-07-09 14:58:34

BENCHMARK: benchmark_testIntToRoman
------------------------------------------------------------
  Error analyzing cpu_atom/L1-dcache-loads/: zero-size array to reduction operation minimum which has no identity
  Error analyzing cpu_atom/L1-dcache-stores/: zero-size array to reduction operation minimum which has no identity
  Error analyzing cpu_atom/L1-icache-load-misses/: zero-size array to reduction operation minimum which has no identity
  Error analyzing cpu_atom/L1-icache-loads/: zero-size array to reduction operation minimum which has no identity
  Error analyzing cpu_atom/LLC-load-misses/: zero-size array to reduction operation minimum which has no identity
  Error analyzing cpu_atom/LLC-loads/: zero-size array to reduction operation minimum which has no identity
  Error analyzing cpu_atom/LLC-store-misses/: zero-size array to reduction operation minimum which has no identity
  Error analyzing cpu_atom/LLC-stores/: zero-size array to reduction operation minimum which has no identity
  Error analyzing cpu_atom/branch-misses/: zero-size array to reduction operation minimum which has no identity
  Error analyzing cpu_atom/branches/: zero-size array to reduction operation minimum which has no identity
  Error analyzing cpu_atom/cycles/: zero-size array to reduction operation minimum which has no identity
  Error analyzing cpu_atom/dTLB-load-misses/: zero-size array to reduction operation minimum which has no identity
  Error analyzing cpu_atom/dTLB-loads/: zero-size array to reduction operation minimum which has no identity
  Error analyzing cpu_atom/dTLB-store-misses/: zero-size array to reduction operation minimum which has no identity
  Error analyzing cpu_atom/dTLB-stores/: zero-size array to reduction operation minimum which has no identity
  Error analyzing cpu_atom/iTLB-load-misses/: zero-size array to reduction operation minimum which has no identity
  Error analyzing cpu_atom/instructions/: zero-size array to reduction operation minimum which has no identity
  Error analyzing cpu_core/L1-dcache-load-misses/: zero-size array to reduction operation minimum which has no identity
  Error analyzing cpu_core/L1-dcache-loads/: zero-size array to reduction operation minimum which has no identity
  Error analyzing cpu_core/L1-dcache-stores/: zero-size array to reduction operation minimum which has no identity
  Error analyzing cpu_core/L1-icache-load-misses/: zero-size array to reduction operation minimum which has no identity
  Error analyzing cpu_core/LLC-load-misses/: zero-size array to reduction operation minimum which has no identity
  Error analyzing cpu_core/LLC-loads/: zero-size array to reduction operation minimum which has no identity
  Error analyzing cpu_core/LLC-store-misses/: zero-size array to reduction operation minimum which has no identity
  Error analyzing cpu_core/LLC-stores/: zero-size array to reduction operation minimum which has no identity
  Error analyzing cpu_core/branch-misses/: zero-size array to reduction operation minimum which has no identity
  Error analyzing cpu_core/branches/: zero-size array to reduction operation minimum which has no identity
  Error analyzing cpu_core/cycles/: zero-size array to reduction operation minimum which has no identity
  Error analyzing cpu_core/dTLB-load-misses/: zero-size array to reduction operation minimum which has no identity
  Error analyzing cpu_core/dTLB-loads/: zero-size array to reduction operation minimum which has no identity
  Error analyzing cpu_core/dTLB-store-misses/: zero-size array to reduction operation minimum which has no identity
  Error analyzing cpu_core/dTLB-stores/: zero-size array to reduction operation minimum which has no identity
  Error analyzing cpu_core/iTLB-load-misses/: zero-size array to reduction operation minimum which has no identity
  Error analyzing cpu_core/instructions/: zero-size array to reduction operation minimum which has no identity

Metric: gc.alloc.rate
  Copilot: 1525.087719 ± 95.660231
  Ground Truth: 2212.789512 ± 10.147955
  Performance Ratio: 0.689
  Improvement: -31.08%
  Statistical Test: Independent t-test
  P-value: 0.000000
  Significant: Yes
  Effect Size (Cohen's d): -10.110 (large)

Metric: gc.alloc.rate.norm
  Copilot: 4914670.365371 ± 834305.421017
  Ground Truth: 6199391.926033 ± 960217.983240
  Performance Ratio: 0.793
  Improvement: -20.72%
  Statistical Test: Independent t-test
  P-value: 0.007201
  Significant: Yes
  Effect Size (Cohen's d): -1.428 (large)

Metric: gc.count
  Copilot: 1.300000 ± 0.458258
  Ground Truth: 1.900000 ± 0.300000
  Performance Ratio: 0.684
  Improvement: -31.58%
  Statistical Test: Mann-Whitney U test
  P-value: 0.008670
  Significant: Yes
  Effect Size (Cohen's d): -1.549 (large)

Metric: gc.time
  Copilot: 6.200000 ± 3.059412
  Ground Truth: 3.100000 ± 2.022375
  Performance Ratio: 2.000
  Improvement: +100.00%
  Statistical Test: Mann-Whitney U test
  P-value: 0.030010
  Significant: Yes
  Effect Size (Cohen's d): 1.195 (large)

Metric: primaryMetric
  Copilot: 3.082401 ± 0.540131
  Ground Truth: 2.671166 ± 0.412358
  Performance Ratio: 0.867
  Improvement: -13.34%
  Statistical Test: Independent t-test
  P-value: 0.086146
  Significant: No
  Effect Size (Cohen's d): 0.856 (large)
