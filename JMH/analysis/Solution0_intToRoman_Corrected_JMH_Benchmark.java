package jmh.tests;

import org.openjdk.jmh.annotations.*;
import org.openjdk.jmh.infra.Blackhole;
import org.openjdk.jmh.profile.GCProfiler;
import org.openjdk.jmh.profile.LinuxPerfNormProfiler;
import org.openjdk.jmh.results.format.ResultFormatType;
import org.openjdk.jmh.runner.Runner;
import org.openjdk.jmh.runner.RunnerException;
import org.openjdk.jmh.runner.options.Options;
import org.openjdk.jmh.runner.options.OptionsBuilder;
import java.util.concurrent.TimeUnit;

/**
 * 正确的 JMH 基准测试，用于测量 intToRoman 方法的性能
 * 
 * 测量指标：
 * - 运行时性能（吞吐量和平均时间）
 * - 内存使用（使用 GCProfiler）
 * - CPU 性能计数器（使用 LinuxPerfNormProfiler）
 * 
 * 注意：这个版本直接测试算法实现，而不是通过 JUnit 包装
 */
@State(Scope.Thread)
@BenchmarkMode({Mode.Throughput, Mode.AverageTime})
@OutputTimeUnit(TimeUnit.MILLISECONDS)
@Warmup(iterations = 5, time = 1, timeUnit = TimeUnit.SECONDS)
@Measurement(iterations = 10, time = 1, timeUnit = TimeUnit.SECONDS)
@Fork(5)
public class Solution0_intToRoman_Corrected_JMH_Benchmark {

    // 测试数据
    private int[] testNumbers;
    
    @Setup
    public void setup() {
        // 准备测试数据
        testNumbers = new int[]{
            3, 4, 9, 58, 1994, 3999,  // 基本测试用例
            1, 2, 5, 10, 50, 100, 500, 1000,  // 边界值
            27, 444, 666, 1776, 2021  // 随机值
        };
    }

    /**
     * 基准测试方法：直接测试 intToRoman 算法
     */
    @Benchmark
    public void benchmark_intToRoman_Direct(Blackhole blackhole) {
        for (int num : testNumbers) {
            String result = intToRoman(num);
            blackhole.consume(result);
        }
    }

    /**
     * 基准测试方法：测试单个典型值
     */
    @Benchmark
    public String benchmark_intToRoman_Single(Blackhole blackhole) {
        String result = intToRoman(1994);
        blackhole.consume(result);
        return result;
    }

    /**
     * intToRoman 算法实现
     * 这里应该是您要测试的实际算法
     */
    private String intToRoman(int num) {
        // 标准的 intToRoman 实现
        String[] thousands = {"", "M", "MM", "MMM"};
        String[] hundreds = {"", "C", "CC", "CCC", "CD", "D", "DC", "DCC", "DCCC", "CM"};
        String[] tens = {"", "X", "XX", "XXX", "XL", "L", "LX", "LXX", "LXXX", "XC"};
        String[] ones = {"", "I", "II", "III", "IV", "V", "VI", "VII", "VIII", "IX"};
        
        return thousands[num / 1000] + 
               hundreds[(num % 1000) / 100] + 
               tens[(num % 100) / 10] + 
               ones[num % 10];
    }

    /**
     * 主方法：运行基准测试并配置正确的 profiler
     */
    public static void main(String[] args) throws RunnerException {
        Options opt = new OptionsBuilder()
            .include(Solution0_intToRoman_Corrected_JMH_Benchmark.class.getSimpleName())
            .resultFormat(ResultFormatType.JSON)
            .result("corrected-benchmark-results.json")
            // 内存分析
            .addProfiler(GCProfiler.class)
            // CPU 性能计数器分析（这是正确的 CPU profiler）
            .addProfiler(LinuxPerfNormProfiler.class)
            .build();
        
        new Runner(opt).run();
    }
}
