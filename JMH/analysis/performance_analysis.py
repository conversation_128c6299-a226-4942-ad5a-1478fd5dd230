#!/usr/bin/env python3
"""
Scientific Performance Analysis Tool for Copilot vs Ground Truth Comparison

This script provides statistical analysis and visualization for comparing
JMH benchmark results between Copilot-generated code and ground truth implementations.

Author: Performance Analysis Tool
Date: 2025-07-09
"""

import json
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from pathlib import Path
import argparse
from typing import Dict, List, Tuple, Any
import warnings
warnings.filterwarnings('ignore')

class PerformanceAnalyzer:
    """Scientific performance analysis for JMH benchmark results"""

    def __init__(self):
        self.results = {}
        self.metrics = [
            'primaryMetric',
            'gc.alloc.rate',
            'gc.alloc.rate.norm',
            'gc.count',
            'gc.time'
        ]

    def load_benchmark_data(self, file_path: str, label: str) -> Dict[str, Any]:
        """Load and parse JMH benchmark JSON data"""
        with open(file_path, 'r') as f:
            data = json.load(f)

        parsed_data = {}
        for entry in data:
            benchmark_name = entry['benchmark'].split('.')[-1]
            metric_name = entry.get('mode', 'unknown')

            # Extract key performance metrics
            if benchmark_name not in parsed_data:
                parsed_data[benchmark_name] = {}

            # Primary metric (throughput or average time)
            if 'primaryMetric' in entry:
                parsed_data[benchmark_name]['primaryMetric'] = {
                    'score': entry['primaryMetric']['score'],
                    'scoreError': entry['primaryMetric']['scoreError'],
                    'scoreUnit': entry['primaryMetric']['scoreUnit'],
                    'rawData': entry['primaryMetric']['rawData'][0] if entry['primaryMetric']['rawData'] else [],
                    'mode': entry['mode']
                }

            # Secondary metrics (GC, memory allocation, etc.)
            if 'secondaryMetrics' in entry:
                for metric_key, metric_data in entry['secondaryMetrics'].items():
                    parsed_data[benchmark_name][metric_key] = {
                        'score': metric_data['score'],
                        'scoreError': metric_data['scoreError'],
                        'scoreUnit': metric_data['scoreUnit'],
                        'rawData': metric_data['rawData'][0] if metric_data['rawData'] else []
                    }

        self.results[label] = parsed_data
        return parsed_data

    def statistical_comparison(self, metric_name: str, benchmark_name: str) -> Dict[str, Any]:
        """Perform statistical comparison between copilot and ground truth"""
        if len(self.results) != 2:
            raise ValueError("Need exactly 2 datasets for comparison")

        labels = list(self.results.keys())
        data1 = self.results[labels[0]][benchmark_name][metric_name]['rawData']
        data2 = self.results[labels[1]][benchmark_name][metric_name]['rawData']

        # Basic statistics
        stats_result = {
            'metric': metric_name,
            'benchmark': benchmark_name,
            'dataset1': {
                'label': labels[0],
                'mean': np.mean(data1),
                'std': np.std(data1),
                'median': np.median(data1),
                'min': np.min(data1),
                'max': np.max(data1),
                'n': len(data1)
            },
            'dataset2': {
                'label': labels[1],
                'mean': np.mean(data2),
                'std': np.std(data2),
                'median': np.median(data2),
                'min': np.min(data2),
                'max': np.max(data2),
                'n': len(data2)
            }
        }

        # Statistical tests
        # 1. Shapiro-Wilk test for normality
        shapiro1 = stats.shapiro(data1)
        shapiro2 = stats.shapiro(data2)

        # 2. Choose appropriate test based on normality
        if shapiro1.pvalue > 0.05 and shapiro2.pvalue > 0.05:
            # Both normal - use t-test
            ttest_result = stats.ttest_ind(data1, data2)
            test_name = "Independent t-test"
            test_statistic = ttest_result.statistic
            p_value = ttest_result.pvalue
        else:
            # Non-normal - use Mann-Whitney U test
            mannwhitney_result = stats.mannwhitneyu(data1, data2, alternative='two-sided')
            test_name = "Mann-Whitney U test"
            test_statistic = mannwhitney_result.statistic
            p_value = mannwhitney_result.pvalue

        # Effect size (Cohen's d)
        pooled_std = np.sqrt(((len(data1) - 1) * np.var(data1) + (len(data2) - 1) * np.var(data2)) /
                           (len(data1) + len(data2) - 2))
        cohens_d = (np.mean(data1) - np.mean(data2)) / pooled_std

        # Performance ratio
        if metric_name == 'primaryMetric':
            mode = self.results[labels[0]][benchmark_name][metric_name]['mode']
            if mode == 'thrpt':  # Higher is better
                performance_ratio = np.mean(data1) / np.mean(data2)
                improvement = ((np.mean(data1) - np.mean(data2)) / np.mean(data2)) * 100
            else:  # Lower is better (avgt)
                performance_ratio = np.mean(data2) / np.mean(data1)
                improvement = ((np.mean(data2) - np.mean(data1)) / np.mean(data1)) * 100
        else:
            performance_ratio = np.mean(data1) / np.mean(data2)
            improvement = ((np.mean(data1) - np.mean(data2)) / np.mean(data2)) * 100

        stats_result.update({
            'normality_test1': {'statistic': shapiro1.statistic, 'p_value': shapiro1.pvalue},
            'normality_test2': {'statistic': shapiro2.statistic, 'p_value': shapiro2.pvalue},
            'comparison_test': {
                'name': test_name,
                'statistic': test_statistic,
                'p_value': p_value,
                'significant': p_value < 0.05
            },
            'effect_size': {
                'cohens_d': cohens_d,
                'interpretation': self._interpret_cohens_d(cohens_d)
            },
            'performance_ratio': performance_ratio,
            'improvement_percentage': improvement
        })

        return stats_result

    def _interpret_cohens_d(self, d: float) -> str:
        """Interpret Cohen's d effect size"""
        abs_d = abs(d)
        if abs_d < 0.2:
            return "negligible"
        elif abs_d < 0.5:
            return "small"
        elif abs_d < 0.8:
            return "medium"
        else:
            return "large"

    def create_visualization(self, output_dir: str = "."):
        """Create visualization charts for performance comparison"""
        if len(self.results) != 2:
            raise ValueError("Need exactly 2 datasets for comparison")

        labels = list(self.results.keys())
        benchmarks = list(set(self.results[labels[0]].keys()).intersection(set(self.results[labels[1]].keys())))

        # Set up the plotting style
        plt.style.use('seaborn-v0_8')
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('Performance Comparison: Copilot vs Ground Truth', fontsize=16, fontweight='bold')

        for benchmark in benchmarks:
            # Primary metric comparison
            if 'primaryMetric' in self.results[labels[0]][benchmark] and 'primaryMetric' in self.results[labels[1]][benchmark]:
                data1 = self.results[labels[0]][benchmark]['primaryMetric']['rawData']
                data2 = self.results[labels[1]][benchmark]['primaryMetric']['rawData']
                unit = self.results[labels[0]][benchmark]['primaryMetric']['scoreUnit']

                # Box plot
                axes[0,0].boxplot([data1, data2], labels=labels)
                axes[0,0].set_title(f'Primary Metric Distribution\n({unit})')
                axes[0,0].set_ylabel(f'Performance ({unit})')

                # Bar plot with error bars
                means = [np.mean(data1), np.mean(data2)]
                stds = [np.std(data1), np.std(data2)]
                axes[0,1].bar(labels, means, yerr=stds, capsize=5, alpha=0.7)
                axes[0,1].set_title(f'Primary Metric Comparison\n({unit})')
                axes[0,1].set_ylabel(f'Mean Performance ({unit})')

            # GC metrics comparison
            if 'gc.alloc.rate' in self.results[labels[0]][benchmark] and 'gc.alloc.rate' in self.results[labels[1]][benchmark]:
                gc_data1 = self.results[labels[0]][benchmark]['gc.alloc.rate']['rawData']
                gc_data2 = self.results[labels[1]][benchmark]['gc.alloc.rate']['rawData']

                axes[1,0].boxplot([gc_data1, gc_data2], labels=labels)
                axes[1,0].set_title('GC Allocation Rate Distribution\n(MB/sec)')
                axes[1,0].set_ylabel('Allocation Rate (MB/sec)')

                # Memory allocation comparison
                gc_means = [np.mean(gc_data1), np.mean(gc_data2)]
                gc_stds = [np.std(gc_data1), np.std(gc_data2)]
                axes[1,1].bar(labels, gc_means, yerr=gc_stds, capsize=5, alpha=0.7, color=['orange', 'green'])
                axes[1,1].set_title('GC Allocation Rate Comparison\n(MB/sec)')
                axes[1,1].set_ylabel('Mean Allocation Rate (MB/sec)')

        plt.tight_layout()
        chart_file = f"{output_dir}/performance_comparison_chart.png"
        plt.savefig(chart_file, dpi=300, bbox_inches='tight')
        plt.close()

        return chart_file

    def generate_report(self, output_file: str = None) -> str:
        """Generate comprehensive performance analysis report"""
        if len(self.results) != 2:
            raise ValueError("Need exactly 2 datasets for comparison")

        labels = list(self.results.keys())
        report_lines = []

        # Header
        report_lines.append("=" * 80)
        report_lines.append("SCIENTIFIC PERFORMANCE ANALYSIS REPORT")
        report_lines.append("=" * 80)
        report_lines.append(f"Comparison: {labels[0]} vs {labels[1]}")
        report_lines.append(f"Analysis Date: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append("")

        # Get common benchmarks
        benchmarks1 = set(self.results[labels[0]].keys())
        benchmarks2 = set(self.results[labels[1]].keys())
        common_benchmarks = benchmarks1.intersection(benchmarks2)

        if not common_benchmarks:
            report_lines.append("ERROR: No common benchmarks found between datasets")
            return "\n".join(report_lines)

        # Analyze each benchmark
        for benchmark in sorted(common_benchmarks):
            report_lines.append(f"BENCHMARK: {benchmark}")
            report_lines.append("-" * 60)

            # Get common metrics
            metrics1 = set(self.results[labels[0]][benchmark].keys())
            metrics2 = set(self.results[labels[1]][benchmark].keys())
            common_metrics = metrics1.intersection(metrics2)

            for metric in sorted(common_metrics):
                try:
                    stats_result = self.statistical_comparison(metric, benchmark)

                    report_lines.append(f"\nMetric: {metric}")
                    report_lines.append(f"  {labels[0]}: {stats_result['dataset1']['mean']:.6f} ± {stats_result['dataset1']['std']:.6f}")
                    report_lines.append(f"  {labels[1]}: {stats_result['dataset2']['mean']:.6f} ± {stats_result['dataset2']['std']:.6f}")
                    report_lines.append(f"  Performance Ratio: {stats_result['performance_ratio']:.3f}")
                    report_lines.append(f"  Improvement: {stats_result['improvement_percentage']:+.2f}%")
                    report_lines.append(f"  Statistical Test: {stats_result['comparison_test']['name']}")
                    report_lines.append(f"  P-value: {stats_result['comparison_test']['p_value']:.6f}")
                    report_lines.append(f"  Significant: {'Yes' if stats_result['comparison_test']['significant'] else 'No'}")
                    report_lines.append(f"  Effect Size (Cohen's d): {stats_result['effect_size']['cohens_d']:.3f} ({stats_result['effect_size']['interpretation']})")

                except Exception as e:
                    report_lines.append(f"  Error analyzing {metric}: {str(e)}")

            report_lines.append("")

        report_content = "\n".join(report_lines)

        if output_file:
            with open(output_file, 'w') as f:
                f.write(report_content)
            print(f"Report saved to: {output_file}")

        return report_content

def main():
    parser = argparse.ArgumentParser(description='Scientific Performance Analysis Tool')
    parser.add_argument('--copilot', required=True, help='Path to Copilot benchmark JSON file')
    parser.add_argument('--ground-truth', required=True, help='Path to Ground Truth benchmark JSON file')
    parser.add_argument('--output', help='Output report file path')

    args = parser.parse_args()

    # Initialize analyzer
    analyzer = PerformanceAnalyzer()

    # Load data
    print("Loading benchmark data...")
    analyzer.load_benchmark_data(args.copilot, "Copilot")
    analyzer.load_benchmark_data(args.ground_truth, "Ground Truth")

    # Generate report
    print("Generating analysis report...")
    report = analyzer.generate_report(args.output)

    # Generate visualization
    print("Creating visualization charts...")
    chart_file = analyzer.create_visualization(".")
    print(f"Chart saved to: {chart_file}")

    # Print to console if no output file specified
    if not args.output:
        print(report)

if __name__ == "__main__":
    main()
