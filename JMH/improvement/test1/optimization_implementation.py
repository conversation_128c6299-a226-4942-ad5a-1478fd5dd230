#!/usr/bin/env python3
"""
基于JMH实验数据的AI代码生成性能优化实现
结合Copilot和DeepSeek的实验结果设计优化策略
"""

import numpy as np
import torch
import torch.nn as nn
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
import subprocess
import json
from concurrent.futures import ThreadPoolExecutor
import tempfile
import os

@dataclass
class PerformanceTarget:
    """性能优化目标"""
    target_cpi: float = 1.5      # 目标综合性能指数
    target_psi: float = 1.0      # 目标稳定性指数
    target_aer: float = 1.2      # 目标架构效率
    weight_cpi: float = 0.5      # CPI权重
    weight_psi: float = 0.3      # PSI权重
    weight_aer: float = 0.2      # AER权重

class PerformancePredictor(nn.Module):
    """基于代码特征预测性能指标的神经网络"""
    
    def __init__(self, vocab_size: int, hidden_dim: int = 512):
        super().__init__()
        self.embedding = nn.Embedding(vocab_size, hidden_dim)
        self.lstm = nn.LSTM(hidden_dim, hidden_dim, batch_first=True, bidirectional=True)
        
        # 三个预测头分别预测CPI、PSI、AER
        self.cpi_head = nn.Sequential(
            nn.Linear(hidden_dim * 2, 256),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(256, 1),
            nn.Sigmoid()  # 输出0-1，然后缩放到合理范围
        )
        
        self.psi_head = nn.Sequential(
            nn.Linear(hidden_dim * 2, 256),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(256, 1),
            nn.Sigmoid()
        )
        
        self.aer_head = nn.Sequential(
            nn.Linear(hidden_dim * 2, 256),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(256, 1),
            nn.Sigmoid()
        )
    
    def forward(self, token_ids: torch.Tensor) -> Dict[str, torch.Tensor]:
        """预测代码的性能指标"""
        # token_ids: [batch_size, seq_len]
        embeddings = self.embedding(token_ids)  # [batch_size, seq_len, hidden_dim]
        
        lstm_out, (hidden, _) = self.lstm(embeddings)
        # 使用最后一个时间步的输出
        final_hidden = torch.cat([hidden[0], hidden[1]], dim=-1)  # [batch_size, hidden_dim*2]
        
        # 预测三个指标
        cpi_pred = self.cpi_head(final_hidden) * 3.0  # 缩放到0-3范围
        psi_pred = self.psi_head(final_hidden) * 2.0  # 缩放到0-2范围
        aer_pred = self.aer_head(final_hidden) * 3.0  # 缩放到0-3范围
        
        return {
            'cpi': cpi_pred.squeeze(-1),
            'psi': psi_pred.squeeze(-1),
            'aer': aer_pred.squeeze(-1)
        }

class PerformanceAwareDecoder:
    """性能感知的解码器"""
    
    def __init__(self, base_model, performance_predictor: PerformancePredictor, tokenizer):
        self.base_model = base_model
        self.perf_predictor = performance_predictor
        self.tokenizer = tokenizer
        
    def decode_with_performance_guidance(self, 
                                       prompt: str, 
                                       target: PerformanceTarget,
                                       max_length: int = 512,
                                       num_beams: int = 5) -> List[str]:
        """性能引导的解码"""
        
        # 编码输入
        input_ids = self.tokenizer.encode(prompt, return_tensors='pt')
        
        # 性能感知的beam search
        candidates = []
        
        for _ in range(num_beams):
            current_ids = input_ids.clone()
            current_score = 0.0
            
            for step in range(max_length):
                # 获取下一个token的概率分布
                with torch.no_grad():
                    outputs = self.base_model(current_ids)
                    next_token_logits = outputs.logits[0, -1, :]
                    next_token_probs = torch.softmax(next_token_logits, dim=-1)
                
                # 获取top-k候选token
                top_k = 50
                top_probs, top_indices = torch.topk(next_token_probs, top_k)
                
                # 为每个候选token预测性能
                best_token = None
                best_score = float('-inf')
                
                for i, token_id in enumerate(top_indices):
                    # 构造候选序列
                    candidate_ids = torch.cat([current_ids, token_id.unsqueeze(0).unsqueeze(0)], dim=1)
                    
                    # 预测性能指标
                    perf_pred = self.perf_predictor(candidate_ids)
                    
                    # 计算综合分数
                    lm_score = torch.log(top_probs[i]).item()
                    perf_score = self.compute_performance_score(perf_pred, target)
                    
                    # 组合语言模型分数和性能分数
                    combined_score = 0.7 * lm_score + 0.3 * perf_score
                    
                    if combined_score > best_score:
                        best_score = combined_score
                        best_token = token_id
                
                # 添加最佳token
                if best_token is not None:
                    current_ids = torch.cat([current_ids, best_token.unsqueeze(0).unsqueeze(0)], dim=1)
                    current_score += best_score
                
                # 检查是否结束
                if best_token == self.tokenizer.eos_token_id:
                    break
            
            # 解码生成的序列
            generated_code = self.tokenizer.decode(current_ids[0], skip_special_tokens=True)
            candidates.append((generated_code, current_score))
        
        # 按分数排序并返回
        candidates.sort(key=lambda x: x[1], reverse=True)
        return [code for code, _ in candidates]
    
    def compute_performance_score(self, perf_pred: Dict[str, torch.Tensor], target: PerformanceTarget) -> float:
        """计算性能分数"""
        cpi_score = -abs(perf_pred['cpi'].item() - target.target_cpi)
        psi_score = -abs(perf_pred['psi'].item() - target.target_psi)
        aer_score = -abs(perf_pred['aer'].item() - target.target_aer)
        
        weighted_score = (target.weight_cpi * cpi_score + 
                         target.weight_psi * psi_score + 
                         target.weight_aer * aer_score)
        
        return weighted_score

class JMHPerformanceEvaluator:
    """JMH性能评估器"""
    
    def __init__(self, jmh_project_path: str):
        self.jmh_project_path = jmh_project_path
        
    def evaluate_code_performance(self, java_code: str, class_name: str) -> Optional[Dict]:
        """评估Java代码的JMH性能"""
        
        try:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(mode='w', suffix='.java', delete=False) as f:
                f.write(java_code)
                temp_file = f.name
            
            # 编译代码
            compile_result = subprocess.run([
                'javac', '-cp', f'{self.jmh_project_path}/target/classes', temp_file
            ], capture_output=True, text=True)
            
            if compile_result.returncode != 0:
                return None  # 编译失败
            
            # 运行JMH基准测试
            jmh_result = subprocess.run([
                'java', '-cp', f'{self.jmh_project_path}/target/classes', 
                'org.openjdk.jmh.Main', class_name, '-f', '1', '-wi', '3', '-i', '5', '-rf', 'json'
            ], capture_output=True, text=True)
            
            if jmh_result.returncode != 0:
                return None  # 运行失败
            
            # 解析JMH结果
            jmh_data = json.loads(jmh_result.stdout)
            return jmh_data[0] if jmh_data else None
            
        except Exception as e:
            print(f"JMH评估错误: {e}")
            return None
        finally:
            # 清理临时文件
            if os.path.exists(temp_file):
                os.unlink(temp_file)

class PerformanceFeedbackTrainer:
    """基于性能反馈的强化学习训练器"""
    
    def __init__(self, model, tokenizer, jmh_evaluator: JMHPerformanceEvaluator):
        self.model = model
        self.tokenizer = tokenizer
        self.jmh_evaluator = jmh_evaluator
        self.optimizer = torch.optim.AdamW(model.parameters(), lr=1e-5)
        
    def compute_reward(self, generated_code: str, problem_context: str) -> float:
        """基于JMH结果计算奖励"""
        
        # 提取类名（简化实现）
        class_name = self.extract_class_name(generated_code)
        if not class_name:
            return -10.0  # 无效代码
        
        # 运行JMH评估
        jmh_result = self.jmh_evaluator.evaluate_code_performance(generated_code, class_name)
        
        if jmh_result is None:
            return -5.0  # 编译或运行失败
        
        # 基于您的实验数据计算奖励
        reward = 0.0
        
        # CPI奖励（基于Copilot的1.983和DeepSeek的1.002）
        throughput = jmh_result.get('primaryMetric', {}).get('score', 0)
        if throughput > 0:
            # 简化的CPI计算（实际应该使用完整的CPI公式）
            estimated_cpi = min(throughput / 0.5, 3.0)  # 假设基准吞吐量为0.5
            
            if estimated_cpi > 1.5:
                reward += 2.0
            elif estimated_cpi > 1.0:
                reward += 1.0
            else:
                reward -= 1.0
        
        # 稳定性奖励（基于原始数据的变异系数）
        raw_data = jmh_result.get('primaryMetric', {}).get('rawData', [])
        if raw_data:
            flattened = [item for sublist in raw_data for item in sublist]
            if len(flattened) > 1:
                cv = np.std(flattened) / np.mean(flattened) if np.mean(flattened) > 0 else float('inf')
                if cv < 0.2:  # 低变异系数
                    reward += 1.0
                elif cv > 0.5:  # 高变异系数
                    reward -= 0.5
        
        return reward
    
    def extract_class_name(self, code: str) -> Optional[str]:
        """从代码中提取类名"""
        lines = code.split('\n')
        for line in lines:
            if 'class ' in line and '{' in line:
                parts = line.split('class ')[1].split('{')[0].strip()
                return parts.split()[0]  # 获取类名
        return None
    
    def train_step(self, batch_problems: List[str]) -> Dict[str, float]:
        """执行一步强化学习训练"""
        
        total_reward = 0.0
        total_loss = 0.0
        
        for problem in batch_problems:
            # 生成代码
            input_ids = self.tokenizer.encode(problem, return_tensors='pt')
            
            with torch.no_grad():
                outputs = self.model.generate(
                    input_ids, 
                    max_length=512, 
                    num_return_sequences=1,
                    do_sample=True,
                    temperature=0.8,
                    return_dict_in_generate=True,
                    output_scores=True
                )
            
            generated_ids = outputs.sequences[0]
            generated_code = self.tokenizer.decode(generated_ids, skip_special_tokens=True)
            
            # 计算奖励
            reward = self.compute_reward(generated_code, problem)
            total_reward += reward
            
            # 计算策略梯度损失
            log_probs = []
            for i, score in enumerate(outputs.scores):
                if i < len(generated_ids) - len(input_ids[0]):
                    token_id = generated_ids[len(input_ids[0]) + i]
                    log_prob = torch.log_softmax(score[0], dim=-1)[token_id]
                    log_probs.append(log_prob)
            
            if log_probs:
                policy_loss = -torch.stack(log_probs).mean() * reward
                total_loss += policy_loss.item()
                
                # 反向传播
                self.optimizer.zero_grad()
                policy_loss.backward()
                self.optimizer.step()
        
        return {
            'avg_reward': total_reward / len(batch_problems),
            'avg_loss': total_loss / len(batch_problems)
        }

class OptimizationExperiment:
    """优化实验框架"""
    
    def __init__(self):
        self.jmh_evaluator = JMHPerformanceEvaluator('/path/to/jmh/project')
        
    def run_baseline_comparison(self, test_problems: List[str]) -> Dict:
        """运行基线对比实验"""
        
        results = {
            'copilot_baseline': [],
            'deepseek_baseline': [],
            'performance_aware_decoding': [],
            'performance_feedback_rl': []
        }
        
        # 这里需要集成实际的模型API
        # 示例框架展示实验流程
        
        for problem in test_problems:
            # 基线结果（使用您的实验数据）
            results['copilot_baseline'].append({
                'cpi': 1.983,  # 来自您的实验
                'psi': 1.291,
                'aer': 2.171,
                'success_rate': 0.811
            })
            
            results['deepseek_baseline'].append({
                'cpi': 1.002,  # 来自您的实验
                'psi': 1.002,
                'aer': 0.977,
                'success_rate': 0.870
            })
            
            # 优化方法的结果需要实际运行获得
            # 这里展示预期的改进
            results['performance_aware_decoding'].append({
                'cpi': 1.5,    # 预期改进
                'psi': 1.1,
                'aer': 1.8,
                'success_rate': 0.85
            })
        
        return results
    
    def statistical_analysis(self, results: Dict) -> Dict:
        """统计显著性分析"""
        from scipy.stats import wilcoxon, ttest_rel
        
        analysis = {}
        
        # 比较不同方法的CPI
        copilot_cpi = [r['cpi'] for r in results['copilot_baseline']]
        optimized_cpi = [r['cpi'] for r in results['performance_aware_decoding']]
        
        # Wilcoxon符号秩检验
        statistic, p_value = wilcoxon(copilot_cpi, optimized_cpi)
        
        analysis['cpi_improvement'] = {
            'statistic': statistic,
            'p_value': p_value,
            'significant': p_value < 0.05,
            'effect_size': (np.mean(optimized_cpi) - np.mean(copilot_cpi)) / np.std(copilot_cpi)
        }
        
        return analysis

# 使用示例
if __name__ == "__main__":
    # 初始化组件
    performance_predictor = PerformancePredictor(vocab_size=50000)
    
    # 定义性能目标
    target = PerformanceTarget(
        target_cpi=1.5,   # 介于DeepSeek(1.002)和Copilot(1.983)之间
        target_psi=1.0,   # 目标稳定性
        target_aer=1.5,   # 目标架构效率
    )
    
    print("AI代码生成性能优化框架初始化完成")
    print(f"目标CPI: {target.target_cpi}")
    print(f"目标PSI: {target.target_psi}")
    print(f"目标AER: {target.target_aer}")
    
    # 运行实验
    experiment = OptimizationExperiment()
    print("实验框架准备就绪，可以开始性能优化实验")
