#!/bin/bash

# Display help information
show_help() {
    echo "Usage: $0 [options]"
    echo ""
    echo "Options:"
    echo "  --help                Display this help information"
    echo "  --ground-truth PATH   Specify benchmark results path (required)"
    echo "  --all                 Run all test classes (default)"
    echo "  --sample              Run only 5 sample test classes"
    echo "  --limit N             Limit the number of test classes to N"
    echo "  --jmh-params P        Set JMH parameters (default: '-f 5 -i 10 -wi 5 -r 1s -w 1s')"
    echo ""
    exit 0
}

# Default settings
GROUND_TRUTH_PATH=""
RUN_ALL=true
SAMPLE_ONLY=false
TEST_LIMIT=0
JMH_PARAMS="-f 5 -i 10 -wi 5 -r 1s -w 1s -v EXTRA"

# Parse command line arguments
while [ $# -gt 0 ]; do
    case "$1" in
        --help)
            show_help
            ;;
        --ground-truth)
            GROUND_TRUTH_PATH="$2"
            shift 2
            ;;
        --all)
            RUN_ALL=true
            SAMPLE_ONLY=false
            shift
            ;;
        --sample)
            RUN_ALL=false
            SAMPLE_ONLY=true
            shift
            ;;
        --limit)
            RUN_ALL=false
            SAMPLE_ONLY=false
            TEST_LIMIT=$2
            shift 2
            ;;
        --jmh-params)
            JMH_PARAMS="$2"
            shift 2
            ;;
        *)
            echo "Unknown option: $1"
            show_help
            ;;
    esac
done

# Check required parameters
if [ -z "$GROUND_TRUTH_PATH" ]; then
    echo "Error: Benchmark results path must be specified (--ground-truth PATH)"
    show_help
fi

# Check if benchmark results path exists
if [ ! -d "$GROUND_TRUTH_PATH" ]; then
    echo "Error: Benchmark results path does not exist: $GROUND_TRUTH_PATH"
    exit 1
fi

# Set project path
COPILOT_DIR="/home/<USER>/math-conda/JMH/models/copilot/copilot"

# Create results directory
DATE_PREFIX=$(date +"%Y%m%d_%H%M%S")
RESULTS_DIR="/home/<USER>/math-conda/JMH/JMH-perfnorm/benchmark_results/${DATE_PREFIX}_copilot"
mkdir -p "$RESULTS_DIR"

# Create log file
LOG_FILE="$RESULTS_DIR/benchmark_run.log"
touch "$LOG_FILE"

# Check perf_event_paranoid setting
PERF_PARANOID=$(cat /proc/sys/kernel/perf_event_paranoid)
echo "Current perf_event_paranoid setting: $PERF_PARANOID" | tee -a "$LOG_FILE"

if [ "$PERF_PARANOID" -gt 1 ]; then
    echo "Warning: perf_event_paranoid setting too high ($PERF_PARANOID)" | tee -a "$LOG_FILE"
    echo "Root privileges needed to lower setting, trying with sudo..." | tee -a "$LOG_FILE"

    # Try to lower perf_event_paranoid setting
    sudo sh -c 'echo 1 > /proc/sys/kernel/perf_event_paranoid' 2>&1 | tee -a "$LOG_FILE"

    # Check if successful
    PERF_PARANOID=$(cat /proc/sys/kernel/perf_event_paranoid)
    echo "perf_event_paranoid setting after adjustment: $PERF_PARANOID" | tee -a "$LOG_FILE"

    if [ "$PERF_PARANOID" -gt 1 ]; then
        echo "Error: Unable to lower perf_event_paranoid setting, will use alternative profilers" | tee -a "$LOG_FILE"
        USE_PERFNORM=false
    else
        echo "Successfully lowered perf_event_paranoid setting, will use perfnorm profiler" | tee -a "$LOG_FILE"
        USE_PERFNORM=true
    fi
else
    echo "perf_event_paranoid setting normal, will use perfnorm profiler" | tee -a "$LOG_FILE"
    USE_PERFNORM=true
fi

# Record start time
echo "===== Starting Copilot benchmark run $(date) =====" | tee -a "$LOG_FILE"
if [ "$USE_PERFNORM" = true ]; then
    echo "Using perfnorm profiler to collect real CPU performance counter data" | tee -a "$LOG_FILE"
else
    echo "Using cl and comp profilers to collect CPU performance data (alternative)" | tee -a "$LOG_FILE"
fi
echo "Using JMH parameters: $JMH_PARAMS" | tee -a "$LOG_FILE"
echo "Benchmark results path: $GROUND_TRUTH_PATH" | tee -a "$LOG_FILE"

# Determine which test classes to run
if [ "$SAMPLE_ONLY" = true ]; then
    # Use predefined 5 sample test classes
    echo "Using 5 sample test classes..." | tee -a "$LOG_FILE"
    SELECTED_TESTS=(
        "Solution11_countPrimes_0_0_Test"           # Math algorithm
        "Solution37_findMinHeightTrees_0_0_Test"    # Graph algorithm
        "Solution63_numRabbits_0_0_Test"            # Greedy algorithm
        "Solution116_checkOverlap_0_0_Test"         # Geometry problem
        "Solution147_findTheWinner_0_0_Test"        # Josephus problem
    )
else
    # Automatically discover all available test classes
    echo "Finding all available test classes..." | tee -a "$LOG_FILE"

    # Find all test classes in benchmark results
    GROUND_TRUTH_TESTS=()
    for JSON_FILE in $(find "$GROUND_TRUTH_PATH" -name "*.json" | sort); do
        # Extract test class name
        TEST_CLASS=$(basename "$JSON_FILE" .json)
        GROUND_TRUTH_TESTS+=("$TEST_CLASS")
    done

    # Find all test classes in Copilot project
    cd "$COPILOT_DIR"
    COPILOT_TESTS=()
    for TEST_FILE in $(find src/test/java/jmh/tests -name "*_Test.java" | sort); do
        # Extract test class name
        TEST_CLASS=$(basename "$TEST_FILE" .java)
        # Check if corresponding benchmark class exists
        if [ -f "src/main/java/jmh/tests/${TEST_CLASS}_JU5Benchmark.java" ]; then
            COPILOT_TESTS+=("$TEST_CLASS")
        fi
    done

    # Find test classes that exist in both projects
    ALL_TESTS=()
    for TEST_CLASS in "${GROUND_TRUTH_TESTS[@]}"; do
        if [[ " ${COPILOT_TESTS[*]} " =~ " ${TEST_CLASS} " ]]; then
            ALL_TESTS+=("$TEST_CLASS")
        fi
    done

    # Select test classes based on limit
    if [ "$TEST_LIMIT" -gt 0 ] && [ "$TEST_LIMIT" -lt "${#ALL_TESTS[@]}" ]; then
        echo "Limiting number of test classes to $TEST_LIMIT..." | tee -a "$LOG_FILE"
        SELECTED_TESTS=("${ALL_TESTS[@]:0:$TEST_LIMIT}")
    else
        SELECTED_TESTS=("${ALL_TESTS[@]}")
    fi
fi

# Output number of selected test classes
echo "Will run benchmarks for ${#SELECTED_TESTS[@]} test classes" | tee -a "$LOG_FILE"

# Record selected test classes
echo "Selected test classes:" | tee -a "$LOG_FILE"
for TEST_CLASS in "${SELECTED_TESTS[@]}"; do
    echo "  - $TEST_CLASS" | tee -a "$LOG_FILE"
done

# Modify run_benchmark.sh script to use perfnorm profiler
modify_benchmark_script() {
    local script_path="$1"
    local temp_script="${script_path}.temp"

    if [ "$USE_PERFNORM" = true ]; then
        # Modify script to use perfnorm profiler
        sed 's/JMH_CMD="$JMH_CMD -prof cl"/JMH_CMD="$JMH_CMD -prof perfnorm"/' "$script_path" > "$temp_script"
        sed -i 's/JMH_CMD="$JMH_CMD -prof comp"/# JMH_CMD="$JMH_CMD -prof comp"/' "$temp_script"
    else
        # Keep original
        cp "$script_path" "$temp_script"
    fi

    # Backup original script
    cp "$script_path" "${script_path}.bak"

    # Use modified script
    cp "$temp_script" "$script_path"
    chmod +x "$script_path"
}

# Restore original script
restore_benchmark_script() {
    local script_path="$1"

    # Restore original script
    if [ -f "${script_path}.bak" ]; then
        cp "${script_path}.bak" "$script_path"
        rm "${script_path}.bak"
    fi

    # Delete temporary script
    if [ -f "${script_path}.temp" ]; then
        rm "${script_path}.temp"
    fi
}

# Run benchmarks
echo "===== Running Copilot benchmarks =====" | tee -a "$LOG_FILE"

# Modify benchmark script
echo "Modifying benchmark script to use appropriate profiler..." | tee -a "$LOG_FILE"
modify_benchmark_script "$COPILOT_DIR/run_benchmark.sh"

# Run benchmarks
echo "Running Copilot benchmarks..." | tee -a "$LOG_FILE"
cd "$COPILOT_DIR"

for TEST_CLASS in "${SELECTED_TESTS[@]}"; do
    echo "Running benchmark: $TEST_CLASS" | tee -a "$LOG_FILE"
    OUTPUT_FILE="$RESULTS_DIR/${TEST_CLASS}.json"
    ./run_benchmark.sh "$TEST_CLASS" --all --json --output "$OUTPUT_FILE" $JMH_PARAMS 2>&1 | tee -a "$LOG_FILE"
    echo "Completed benchmark: $TEST_CLASS" | tee -a "$LOG_FILE"
    echo "-----------------------------------" | tee -a "$LOG_FILE"
done

# Restore original script
echo "Restoring original benchmark script..." | tee -a "$LOG_FILE"
restore_benchmark_script "$COPILOT_DIR/run_benchmark.sh"

# Record end time
echo "===== Finished running Copilot benchmarks $(date) =====" | tee -a "$LOG_FILE"

echo "Copilot benchmarks completed. Results saved in $RESULTS_DIR" | tee -a "$LOG_FILE"
echo "Log file saved in $LOG_FILE" | tee -a "$LOG_FILE"

# # Analyze benchmark results
# echo "===== Analyzing benchmark results =====" | tee -a "$LOG_FILE"
# cd "/home/<USER>/math-conda/JMH/JMH-perfnorm"

# # Use advanced analysis script
# echo "Running advanced analysis..." | tee -a "$LOG_FILE"
# python3 advanced_metrics.py --ground-truth "$GROUND_TRUTH_PATH" --copilot "$RESULTS_DIR" --output-dir "$RESULTS_DIR/analysis" 2>&1 | tee -a "$LOG_FILE"

# echo "Benchmarks and analysis completed. Results saved in $RESULTS_DIR" | tee -a "$LOG_FILE"
# echo "You can view charts and metrics in the $RESULTS_DIR/analysis directory" | tee -a "$LOG_FILE"
